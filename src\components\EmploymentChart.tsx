
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { dataManager } from '@/utils/dataManager';

const EmploymentChart = () => {
  const [employmentData, setEmploymentData] = useState([]);
  const [industryData, setIndustryData] = useState([]);

  useEffect(() => {
    const loadChartData = () => {
      const alumni = dataManager.getAlumniData();
      const employment = dataManager.getEmploymentData();

      // Generate employment trend data by graduation year
      const yearData = {};
      const currentYear = new Date().getFullYear();

      // Initialize years (last 5 years)
      for (let i = 4; i >= 0; i--) {
        const year = currentYear - i;
        yearData[year] = { year: year.toString(), bekerja: 0, studi: 0, wirausaha: 0, mencari: 0 };
      }

      // Count alumni by graduation year and employment status
      alumni.forEach(alum => {
        if (yearData[alum.tahunLulus]) {
          const emp = employment.find(e => e.alumniId === alum.id);
          if (emp) {
            if (emp.statusPekerjaan === 'bekerja') {
              yearData[alum.tahunLulus].bekerja++;
            } else if (emp.statusPekerjaan === 'wirausaha') {
              yearData[alum.tahunLulus].wirausaha++;
            } else {
              yearData[alum.tahunLulus].mencari++;
            }
          } else {
            // Assume continuing study if no employment data
            yearData[alum.tahunLulus].studi++;
          }
        }
      });

      setEmploymentData(Object.values(yearData));

      // Generate industry distribution data
      const industryCount = {};
      employment.forEach(emp => {
        const industry = emp.jenisUsaha || 'Lainnya';
        industryCount[industry] = (industryCount[industry] || 0) + 1;
      });

      const totalEmployed = Object.values(industryCount).reduce((sum, count) => sum + count, 0);
      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6B7280', '#EC4899', '#14B8A6'];

      const industryChartData = Object.entries(industryCount)
        .map(([name, count], index) => ({
          name,
          value: totalEmployed > 0 ? Math.round((count / totalEmployed) * 100) : 0,
          color: colors[index % colors.length]
        }))
        .sort((a, b) => b.value - a.value);

      setIndustryData(industryChartData);
    };

    loadChartData();
  }, []);
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Employment Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Tren Status Alumni per Tahun</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={employmentData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="year" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="bekerja" stackId="a" fill="#3B82F6" name="Bekerja" />
              <Bar dataKey="studi" stackId="a" fill="#10B981" name="Melanjutkan Studi" />
              <Bar dataKey="wirausaha" stackId="a" fill="#F59E0B" name="Wirausaha" />
              <Bar dataKey="mencari" stackId="a" fill="#EF4444" name="Mencari Kerja" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Industry Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Distribusi Industri Pekerjaan</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={industryData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {industryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmploymentChart;
