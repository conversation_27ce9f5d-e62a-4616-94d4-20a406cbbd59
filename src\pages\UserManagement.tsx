import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Users, UserCheck, UserX, Shield, Edit, Trash2, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import Layout from '@/components/Layout';
import UserForm from '@/components/forms/UserForm';
import { dataManager, UserData } from '@/utils/dataManager';
import { sqliteDataManager } from '@/utils/sqliteDataManager';

const UserManagement = () => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingUsers, setIsGeneratingUsers] = useState(false);
  const [generateResult, setGenerateResult] = useState<any>(null);
  const [showGenerateResult, setShowGenerateResult] = useState(false);

  useEffect(() => {
    loadUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, filterRole, filterStatus]);

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let userData;

      if (isServerAvailable) {
        userData = await sqliteDataManager.getUserData();
      } else {
        userData = dataManager.getUserData();
      }

      setUsers(userData);
    } catch (error) {
      console.error('Failed to load users:', error);
      // Fallback to localStorage
      const userData = dataManager.getUserData();
      setUsers(userData);
    } finally {
      setIsLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        (user.username || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.fullName || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (filterRole !== 'all') {
      filtered = filtered.filter(user => user.role === filterRole);
    }

    // Status filter
    if (filterStatus !== 'all') {
      const isActive = filterStatus === 'active';
      filtered = filtered.filter(user => user.isActive === isActive);
    }

    setFilteredUsers(filtered);
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setIsFormOpen(true);
  };

  const handleViewUser = (user: UserData) => {
    setSelectedUser(user);
    setIsViewDialogOpen(true);
  };

  const handleEditUser = (user: UserData) => {
    setSelectedUser(user);
    setIsFormOpen(true);
  };

  const handleDeleteUser = (user: UserData) => {
    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  const handleGenerateUsersFromAlumni = async () => {
    setIsGeneratingUsers(true);
    try {
      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();

      if (isServerAvailable) {
        const result = await sqliteDataManager.generateUsersFromAlumni();
        setGenerateResult(result);
        setShowGenerateResult(true);
        loadUsers(); // Refresh the user list
      } else {
        alert('Fitur ini memerlukan koneksi ke database server');
      }
    } catch (error) {
      console.error('Error generating users from alumni:', error);
      alert('Gagal membuat pengguna dari data alumni');
    } finally {
      setIsGeneratingUsers(false);
    }
  };

  const handleFormSubmit = async (formData: any) => {
    setIsLoading(true);
    try {
      // Map form data to UserData interface
      const userData: Omit<UserData, 'id' | 'createdAt'> = {
        username: formData.username || '',
        email: formData.email || '',
        role: formData.role || 'alumni',
        namaLengkap: formData.fullName || ''
      };

      // Try SQLite first, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();

      if (isServerAvailable) {
        if (selectedUser) {
          // Update existing user
          await sqliteDataManager.updateUser(selectedUser.id, userData);
        } else {
          // Add new user
          await sqliteDataManager.addUser(userData);
        }
      } else {
        if (selectedUser) {
          // Update existing user
          dataManager.updateUser(selectedUser.id, userData);
        } else {
          // Add new user
          dataManager.addUser(userData);
        }
      }

      loadUsers();
      setIsFormOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error saving user:', error);
      alert('Gagal menyimpan data pengguna');
    } finally {
      setIsLoading(false);
    }
  };

  const confirmDelete = async () => {
    if (selectedUser) {
      setIsLoading(true);
      try {
        // Try SQLite first, fallback to localStorage
        const isServerAvailable = await sqliteDataManager.isServerAvailable();

        if (isServerAvailable) {
          await sqliteDataManager.deleteUser(selectedUser.id);
        } else {
          dataManager.deleteUser(selectedUser.id);
        }

        loadUsers();
        setIsDeleteDialogOpen(false);
        setSelectedUser(null);
      } catch (error) {
        console.error('Error deleting user:', error);
        alert('Gagal menghapus data pengguna');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    setIsLoading(true);
    try {
      dataManager.updateUser(userId, { isActive: !currentStatus });
      loadUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
      alert('Gagal mengubah status pengguna');
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBadge = (role: string) => {
    const roleMap = {
      admin: { label: 'Admin', variant: 'default' as const, color: 'bg-red-600' },
      moderator: { label: 'Moderator', variant: 'secondary' as const, color: 'bg-blue-600' },
      user: { label: 'User', variant: 'outline' as const, color: 'bg-gray-600' }
    };
    
    const roleInfo = roleMap[role as keyof typeof roleMap] || { label: role, variant: 'default' as const, color: 'bg-gray-600' };
    return <Badge variant={roleInfo.variant}>{roleInfo.label}</Badge>;
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Belum pernah login';
    return new Date(dateString).toLocaleString('id-ID');
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Manajemen Pengguna</h1>
            <p className="text-gray-600">Kelola pengguna dan hak akses sistem</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleGenerateUsersFromAlumni}
              disabled={isGeneratingUsers}
            >
              <UserCheck className="h-4 w-4 mr-2" />
              {isGeneratingUsers ? 'Memproses...' : 'Buat Pengguna dari Alumni'}
            </Button>
            <Button className="university-gradient" onClick={handleAddUser}>
              <Plus className="h-4 w-4 mr-2" />
              Tambah Pengguna
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Pengguna</p>
                  <p className="text-2xl font-bold text-gray-900">{users.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pengguna Aktif</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.isActive).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Admin</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => u.role === 'admin').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserX className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Tidak Aktif</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {users.filter(u => !u.isActive).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Cari berdasarkan username, email, atau nama..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={filterRole} onValueChange={setFilterRole}>
                  <SelectTrigger className="w-[140px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Role</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="moderator">Moderator</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Tidak Aktif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users Table */}
        <Card>
          <CardHeader>
            <CardTitle>Daftar Pengguna ({filteredUsers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            {filteredUsers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Belum ada pengguna yang tersedia</p>
                <Button className="mt-4 university-gradient" onClick={handleAddUser}>
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Pengguna Pertama
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Username</TableHead>
                      <TableHead>Nama Lengkap</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Terakhir Login</TableHead>
                      <TableHead>Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.username}</TableCell>
                        <TableCell>{user.fullName}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{getRoleBadge(user.role)}</TableCell>
                        <TableCell>
                          <Badge variant={user.isActive ? 'default' : 'secondary'}>
                            {user.isActive ? 'Aktif' : 'Tidak Aktif'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {formatDate(user.lastLogin)}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="sm" title="Lihat Detail" onClick={() => handleViewUser(user)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm" title="Edit" onClick={() => handleEditUser(user)}>
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-red-600 hover:text-red-700" 
                              title="Hapus"
                              onClick={() => handleDeleteUser(user)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedUser ? 'Edit Pengguna' : 'Tambah Pengguna Baru'}</DialogTitle>
            <DialogDescription>
              {selectedUser ? 'Edit informasi pengguna yang sudah ada' : 'Isi formulir untuk menambahkan pengguna baru'}
            </DialogDescription>
          </DialogHeader>
          <UserForm
            user={selectedUser}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsFormOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus pengguna <strong>{selectedUser?.fullName}</strong> ({selectedUser?.username})? 
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Batal</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete} 
              disabled={isLoading}
              className="bg-red-600 hover:bg-red-700"
            >
              {isLoading ? 'Menghapus...' : 'Hapus'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View User Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detail Pengguna</DialogTitle>
            <DialogDescription>
              Informasi lengkap tentang pengguna yang dipilih
            </DialogDescription>
          </DialogHeader>
          {selectedUser && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Nama Lengkap</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedUser.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedUser.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Role</label>
                  <div className="mt-1">
                    <Badge variant={
                      selectedUser.role === 'admin' ? 'default' :
                      selectedUser.role === 'staff' ? 'secondary' : 'outline'
                    }>
                      {selectedUser.role === 'admin' ? 'Administrator' :
                       selectedUser.role === 'staff' ? 'Staff' : 'Alumni'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="mt-1">
                    <Badge variant={selectedUser.isActive ? 'default' : 'destructive'}>
                      {selectedUser.isActive ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Tanggal Dibuat</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedUser.createdAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Terakhir Login</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedUser.lastLogin ?
                      new Date(selectedUser.lastLogin).toLocaleDateString('id-ID') : 'Belum pernah login'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Terakhir Diupdate</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedUser.updatedAt).toLocaleDateString('id-ID')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">User ID</label>
                  <p className="mt-1 text-sm text-gray-500 font-mono">{selectedUser.id}</p>
                </div>
              </div>

              {/* Permissions or additional info can be added here */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Informasi Akses</label>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">
                    Pengguna ini memiliki akses level <strong>{selectedUser.role}</strong> dan
                    status <strong>{selectedUser.isActive ? 'aktif' : 'tidak aktif'}</strong>.
                  </p>
                  {selectedUser.role === 'admin' && (
                    <p className="text-sm text-gray-600 mt-1">
                      Administrator memiliki akses penuh ke semua fitur sistem.
                    </p>
                  )}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
                  Tutup
                </Button>
                <Button onClick={() => {
                  setIsViewDialogOpen(false);
                  handleEditUser(selectedUser);
                }}>
                  Edit Pengguna
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Generate Users Result Dialog */}
      <Dialog open={showGenerateResult} onOpenChange={setShowGenerateResult}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Hasil Pembuatan Pengguna dari Alumni</DialogTitle>
            <DialogDescription>
              Ringkasan hasil pembuatan akun pengguna dari data alumni
            </DialogDescription>
          </DialogHeader>

          {generateResult && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">{generateResult.created}</p>
                      <p className="text-sm text-gray-500">Pengguna Berhasil Dibuat</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-yellow-600">{generateResult.skipped}</p>
                      <p className="text-sm text-gray-500">Alumni Dilewati</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {generateResult.skippedAlumni && generateResult.skippedAlumni.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-2">Alumni yang Dilewati:</h3>
                  <div className="border rounded-md overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>NIM</TableHead>
                          <TableHead>Nama</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Alasan</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {generateResult.skippedAlumni.map((alumni: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell>{alumni.nim}</TableCell>
                            <TableCell>{alumni.nama_lengkap}</TableCell>
                            <TableCell>{alumni.email}</TableCell>
                            <TableCell>{alumni.reason}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}

              <div className="flex justify-end">
                <Button onClick={() => setShowGenerateResult(false)}>
                  Tutup
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default UserManagement;
