-- SQLite Database Schema for Tracer Alumni System

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role TEXT CHECK(role IN ('admin', 'staff', 'alumni')) NOT NULL DEFAULT 'alumni',
    nama_lengkap TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME
);

-- Alumni table
CREATE TABLE IF NOT EXISTS alumni (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    nim TEXT UNIQUE NOT NULL,
    nama_lengkap TEXT NOT NULL,
    program_studi TEXT,
    fakultas TEXT,
    tahun_masuk INTEGER,
    tahun_lulus INTEGER,
    ipk REAL,
    email TEXT,
    no_telepon TEXT,
    alamat TEXT,
    status_verifikasi TEXT CHECK(status_verifikasi IN ('pending', 'verified', 'rejected')) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Employment table
CREATE TABLE IF NOT EXISTS employment (
    id TEXT PRIMARY KEY,
    alumni_id TEXT NOT NULL,
    nama_perusahaan TEXT NOT NULL,
    posisi_jabatan TEXT NOT NULL,
    jenis_usaha TEXT,
    gaji_pertama REAL,
    gaji_saat_ini REAL,
    tanggal_mulai_kerja DATE,
    status_pekerjaan TEXT CHECK(status_pekerjaan IN ('bekerja', 'tidak_bekerja', 'wirausaha')) DEFAULT 'bekerja',
    relevansi_pekerjaan TEXT CHECK(relevansi_pekerjaan IN ('sangat_relevan', 'relevan', 'kurang_relevan', 'tidak_relevan')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (alumni_id) REFERENCES alumni(id) ON DELETE CASCADE
);

-- Surveys table
CREATE TABLE IF NOT EXISTS surveys (
    id TEXT PRIMARY KEY,
    judul TEXT NOT NULL,
    deskripsi TEXT,
    tanggal_mulai DATE,
    tanggal_selesai DATE,
    status TEXT CHECK(status IN ('active', 'inactive')) DEFAULT 'active',
    target_alumni TEXT, -- JSON array of alumni IDs
    questions TEXT, -- JSON array of questions
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Survey Responses table
CREATE TABLE IF NOT EXISTS survey_responses (
    id TEXT PRIMARY KEY,
    survey_id TEXT NOT NULL,
    alumni_id TEXT NOT NULL,
    responses TEXT NOT NULL, -- JSON object with question_id: answer pairs
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (survey_id) REFERENCES surveys(id) ON DELETE CASCADE,
    FOREIGN KEY (alumni_id) REFERENCES alumni(id) ON DELETE CASCADE
);

-- Reports table
CREATE TABLE IF NOT EXISTS reports (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    type TEXT CHECK(type IN ('employment', 'survey', 'alumni', 'custom')) NOT NULL,
    generated_by TEXT NOT NULL,
    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    data TEXT, -- JSON data
    filters TEXT, -- JSON filters
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id TEXT PRIMARY KEY,
    key TEXT UNIQUE NOT NULL,
    value TEXT NOT NULL, -- JSON value
    description TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_alumni_nim ON alumni(nim);
CREATE INDEX IF NOT EXISTS idx_alumni_email ON alumni(email);
CREATE INDEX IF NOT EXISTS idx_alumni_status ON alumni(status_verifikasi);
CREATE INDEX IF NOT EXISTS idx_alumni_tahun_lulus ON alumni(tahun_lulus);
CREATE INDEX IF NOT EXISTS idx_employment_alumni_id ON employment(alumni_id);
CREATE INDEX IF NOT EXISTS idx_employment_status ON employment(status_pekerjaan);
CREATE INDEX IF NOT EXISTS idx_survey_responses_survey_id ON survey_responses(survey_id);
CREATE INDEX IF NOT EXISTS idx_survey_responses_alumni_id ON survey_responses(alumni_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Insert default admin user
INSERT OR IGNORE INTO users (id, username, email, role, nama_lengkap, is_active, created_at, updated_at)
VALUES ('admin-001', 'admin', '<EMAIL>', 'admin', 'Administrator', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert default settings
INSERT OR IGNORE INTO settings (id, key, value, description, updated_at) VALUES
('set-001', 'university_name', '"Universitas Alumni Connect"', 'Nama universitas', CURRENT_TIMESTAMP),
('set-002', 'contact_email', '"<EMAIL>"', 'Email kontak', CURRENT_TIMESTAMP),
('set-003', 'contact_phone', '"+62 21 1234567"', 'Nomor telepon kontak', CURRENT_TIMESTAMP),
('set-004', 'address', '"Jl. Pendidikan No. 123, Jakarta"', 'Alamat universitas', CURRENT_TIMESTAMP),
('set-005', 'survey_reminder_days', '7', 'Hari pengingat survey', CURRENT_TIMESTAMP),
('set-006', 'email_notifications', 'true', 'Notifikasi email', CURRENT_TIMESTAMP),
('set-007', 'auto_verification', 'false', 'Verifikasi otomatis', CURRENT_TIMESTAMP),
('set-008', 'max_file_size_mb', '10', 'Ukuran maksimal file (MB)', CURRENT_TIMESTAMP),
('set-009', 'session_duration_minutes', '60', 'Durasi sesi (menit)', CURRENT_TIMESTAMP),
('set-010', 'theme', '"light"', 'Tema aplikasi', CURRENT_TIMESTAMP);
