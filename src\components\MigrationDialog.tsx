import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Download, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Server,
  HardDrive
} from 'lucide-react';
import { migrationHelper, MigrationResult } from '@/utils/migrationHelper';

interface MigrationDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const MigrationDialog: React.FC<MigrationDialogProps> = ({ isOpen, onClose }) => {
  const [migrationStatus, setMigrationStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [migrationResult, setMigrationResult] = useState<MigrationResult | null>(null);
  const [currentStep, setCurrentStep] = useState<'status' | 'migrating' | 'completed'>('status');

  useEffect(() => {
    if (isOpen) {
      checkMigrationStatus();
    }
  }, [isOpen]);

  const checkMigrationStatus = async () => {
    setIsLoading(true);
    try {
      const status = await migrationHelper.getMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Failed to check migration status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMigration = async () => {
    setCurrentStep('migrating');
    setIsLoading(true);
    
    try {
      const result = await migrationHelper.migrateToSQLite();
      setMigrationResult(result);
      setCurrentStep('completed');
    } catch (error) {
      setMigrationResult({
        success: false,
        message: 'Migration failed with error',
        details: {
          alumni: 0,
          employment: 0,
          users: 0,
          surveys: 0,
          settings: 0,
          survey_responses: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error']
        }
      });
      setCurrentStep('completed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackup = async () => {
    setIsLoading(true);
    try {
      const result = await migrationHelper.createBackup();
      if (result.success) {
        alert('Backup created successfully and downloaded!');
      } else {
        alert(`Backup failed: ${result.error}`);
      }
    } catch (error) {
      alert('Backup failed');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatusStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <Database className="h-12 w-12 mx-auto mb-4 text-blue-600" />
        <h3 className="text-lg font-semibold mb-2">Database Migration</h3>
        <p className="text-gray-600">
          Migrate your data from localStorage to SQLite database for better performance and reliability.
        </p>
      </div>

      {migrationStatus && (
        <div className="space-y-4">
          {/* Server Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-sm">
                <Server className="h-4 w-4 mr-2" />
                Server Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <span>SQLite Server</span>
                <Badge variant={migrationStatus.serverAvailable ? 'default' : 'destructive'}>
                  {migrationStatus.serverAvailable ? 'Available' : 'Unavailable'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Local Data Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-sm">
                <HardDrive className="h-4 w-4 mr-2" />
                Local Data (localStorage)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(migrationStatus.localDataCounts).map(([key, count]) => (
                  <div key={key} className="flex justify-between text-sm">
                    <span className="capitalize">{key}:</span>
                    <span className="font-medium">{count as number} records</span>
                  </div>
                ))}
                <div className="pt-2 border-t">
                  <div className="flex justify-between font-medium">
                    <span>Total:</span>
                    <span>
                      {Object.values(migrationStatus.localDataCounts).reduce((sum: number, count) => sum + (count as number), 0)} records
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SQLite Data Status */}
          {migrationStatus.sqliteDataCounts && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-sm">
                  <Database className="h-4 w-4 mr-2" />
                  SQLite Database
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(migrationStatus.sqliteDataCounts).map(([key, data]) => (
                    <div key={key} className="flex justify-between text-sm">
                      <span className="capitalize">{key}:</span>
                      <span className="font-medium">{(data as any).count} records</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="space-y-3">
            {!migrationStatus.serverAvailable && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  SQLite server is not running. Please start the server with: <code>npm run server:dev</code>
                </AlertDescription>
              </Alert>
            )}

            {migrationStatus.hasLocalData && migrationStatus.serverAvailable && (
              <div className="space-y-2">
                <Button 
                  onClick={handleBackup}
                  variant="outline" 
                  className="w-full"
                  disabled={isLoading}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Create Backup First
                </Button>
                
                <Button 
                  onClick={handleMigration}
                  className="w-full"
                  disabled={isLoading}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Start Migration
                </Button>
              </div>
            )}

            {!migrationStatus.hasLocalData && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No data found in localStorage to migrate.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </div>
      )}
    </div>
  );

  const renderMigratingStep = () => (
    <div className="text-center space-y-6">
      <Loader2 className="h-12 w-12 mx-auto animate-spin text-blue-600" />
      <div>
        <h3 className="text-lg font-semibold mb-2">Migrating Data...</h3>
        <p className="text-gray-600">
          Please wait while we transfer your data to SQLite database.
        </p>
      </div>
      <Progress value={50} className="w-full" />
    </div>
  );

  const renderCompletedStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        {migrationResult?.success ? (
          <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-600" />
        ) : (
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-600" />
        )}
        <h3 className="text-lg font-semibold mb-2">
          {migrationResult?.success ? 'Migration Completed!' : 'Migration Failed'}
        </h3>
        <p className="text-gray-600">
          {migrationResult?.message}
        </p>
      </div>

      {migrationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Migration Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {Object.entries(migrationResult.details).map(([key, value]) => {
                if (key === 'errors') return null;
                return (
                  <div key={key} className="flex justify-between text-sm">
                    <span className="capitalize">{key}:</span>
                    <span className="font-medium">{value as number} records</span>
                  </div>
                );
              })}
              
              {migrationResult.details.errors.length > 0 && (
                <div className="pt-2 border-t">
                  <p className="text-sm font-medium text-red-600 mb-1">Errors:</p>
                  {migrationResult.details.errors.map((error, index) => (
                    <p key={index} className="text-xs text-red-500">{error}</p>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-2">
        {migrationResult?.success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Your application will now use SQLite database. You can safely close this dialog.
            </AlertDescription>
          </Alert>
        )}
        
        <Button onClick={onClose} className="w-full">
          Close
        </Button>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Database Migration</DialogTitle>
          <DialogDescription>
            Migrate your data from localStorage to SQLite database for better performance and reliability.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          {isLoading && currentStep === 'status' && (
            <div className="text-center">
              <Loader2 className="h-8 w-8 mx-auto animate-spin mb-4" />
              <p>Checking migration status...</p>
            </div>
          )}
          
          {!isLoading && currentStep === 'status' && renderStatusStep()}
          {currentStep === 'migrating' && renderMigratingStep()}
          {currentStep === 'completed' && renderCompletedStep()}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MigrationDialog;
