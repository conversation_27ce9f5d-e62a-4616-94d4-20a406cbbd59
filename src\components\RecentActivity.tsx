
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserPlus, FileText, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { dataManager } from '@/utils/dataManager';

interface Activity {
  id: string;
  type: 'registration' | 'survey' | 'verification' | 'update';
  user: string;
  action: string;
  time: string;
  status?: 'completed' | 'pending' | 'failed';
}

const formatTimeAgo = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Baru saja';
  if (diffInMinutes < 60) return `${diffInMinutes} menit yang lalu`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} jam yang lalu`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} hari yang lalu`;

  return date.toLocaleDateString('id-ID');
};

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'registration': return <UserPlus className="h-4 w-4" />;
    case 'survey': return <FileText className="h-4 w-4" />;
    case 'verification': return <CheckCircle className="h-4 w-4" />;
    case 'update': return <AlertCircle className="h-4 w-4" />;
    default: return <Clock className="h-4 w-4" />;
  }
};

const getStatusBadge = (status?: string) => {
  switch (status) {
    case 'completed':
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Selesai</Badge>;
    case 'pending':
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Menunggu</Badge>;
    case 'failed':
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Gagal</Badge>;
    default:
      return null;
  }
};

const RecentActivity = () => {
  const [activities, setActivities] = useState<Activity[]>([]);

  useEffect(() => {
    const loadRecentActivities = () => {
      const alumni = dataManager.getAlumniData();
      const employment = dataManager.getEmploymentData();
      const surveys = dataManager.getSurveyData();
      const users = dataManager.getUserData();

      const recentActivities: Activity[] = [];

      // Recent alumni registrations (last 10)
      const recentAlumni = alumni
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 5);

      recentAlumni.forEach(alum => {
        recentActivities.push({
          id: `alumni-${alum.id}`,
          type: 'registration',
          user: alum.namaLengkap,
          action: 'Mendaftar sebagai alumni baru',
          time: formatTimeAgo(alum.createdAt),
          status: alum.statusVerifikasi === 'verified' ? 'completed' :
                  alum.statusVerifikasi === 'pending' ? 'pending' : 'failed'
        });
      });

      // Recent employment updates (last 5)
      const recentEmployment = employment
        .sort((a, b) => new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime())
        .slice(0, 3);

      recentEmployment.forEach(emp => {
        const alum = alumni.find(a => a.id === emp.alumniId);
        if (alum) {
          recentActivities.push({
            id: `employment-${emp.id}`,
            type: 'update',
            user: alum.namaLengkap,
            action: 'Memperbarui informasi pekerjaan',
            time: formatTimeAgo(emp.updatedAt || emp.createdAt),
            status: 'completed'
          });
        }
      });

      // Recent survey activities
      const activeSurveys = surveys.filter(s => s.status === 'active').slice(0, 2);
      activeSurveys.forEach(survey => {
        recentActivities.push({
          id: `survey-${survey.id}`,
          type: 'survey',
          user: 'Sistem',
          action: `Survey "${survey.title}" sedang aktif`,
          time: formatTimeAgo(survey.createdAt),
          status: 'completed'
        });
      });

      // Sort all activities by time and take latest 8
      const sortedActivities = recentActivities
        .sort((a, b) => {
          // Convert time descriptions back to comparable values for sorting
          const timeToMinutes = (timeStr: string): number => {
            if (timeStr === 'Baru saja') return 0;
            if (timeStr.includes('menit')) return parseInt(timeStr);
            if (timeStr.includes('jam')) return parseInt(timeStr) * 60;
            if (timeStr.includes('hari')) return parseInt(timeStr) * 24 * 60;
            return 999999; // For dates, put them last
          };
          return timeToMinutes(a.time) - timeToMinutes(b.time);
        })
        .slice(0, 8);

      setActivities(sortedActivities);
    };

    loadRecentActivities();
  }, []);

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Aktivitas Terbaru</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Belum ada aktivitas terbaru</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Aktivitas Terbaru</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
              <Avatar className="h-8 w-8">
                <AvatarImage src={`/api/placeholder/32/32?text=${activity.user.charAt(0)}`} />
                <AvatarFallback>{activity.user.charAt(0)}</AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <div className="university-gradient p-1 rounded text-white">
                    {getActivityIcon(activity.type)}
                  </div>
                  <p className="text-sm font-medium text-gray-900">{activity.user}</p>
                  {getStatusBadge(activity.status)}
                </div>
                <p className="text-sm text-gray-600">{activity.action}</p>
                <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivity;
