# Mock Data Guide - Tracer Alumni System

## Overview

This guide explains how to use the mock data system for the Tracer Alumni System. The mock data provides realistic sample data for development, testing, and demonstration purposes.

## 📁 Files Structure

```
data/
├── mock-data.json          # Comprehensive mock data file
└── tracer_alumni.db        # SQLite database file

scripts/
├── load-mock-data.js       # Script to load mock data into SQLite
├── clear-database.js       # Script to clear all database data
└── migrate-to-sqlite.js    # Original migration script
```

## 📊 Mock Data Contents

The `data/mock-data.json` file contains realistic sample data for:

### Alumni (8 records)
- **<PERSON>** - Teknik Informatika, graduated 2024
- **<PERSON>** - Sistem Informasi, graduated 2023
- **<PERSON>** - Teknik Elektro, graduated 2022
- **<PERSON><PERSON>** - <PERSON><PERSON><PERSON><PERSON>, graduated 2023
- **<PERSON><PERSON>** - <PERSON><PERSON><PERSON><PERSON>, graduated 2024
- **<PERSON>** - Pendidikan Bahasa Inggris, graduated 2023
- **<PERSON><PERSON>** - <PERSON><PERSON><PERSON>, graduated 2022
- **Dewi <PERSON>** - Psikologi, graduated 2024

### Employment (8 records)
- Various companies: Tech Corp, Digital Solutions, PT Listrik Negara, Bank Mandiri, etc.
- Different job types: Software Developer, System Analyst, Engineer, Management Trainee
- Salary ranges from 4.5M to 13.5M IDR
- Employment status: bekerja, wirausaha

### Users (9 records)
- Alumni users with different roles
- Staff/admin users
- Realistic login timestamps

### Surveys (2 records)
- **Survey Kepuasan Alumni 2024** - Satisfaction survey with rating and text questions
- **Tracer Study Alumni 2024** - Career tracking survey with multiple choice questions

### Survey Responses (10 records)
- Responses to both surveys from different alumni
- Realistic answers including ratings, text feedback, and multiple choice selections

### Settings (8 records)
- University configuration: name, contact info, address
- System settings: notifications, file limits, session duration

## 🚀 Usage Instructions

### 1. Loading Mock Data

To load the mock data into your SQLite database:

```bash
# Load mock data (with confirmation prompt)
node scripts/load-mock-data.js

# Load mock data automatically (skip confirmation)
node scripts/load-mock-data.js --force

# Clear existing data before loading
node scripts/load-mock-data.js --force --clear
```

### 2. Clearing Database

To clear all data from the database:

```bash
# Clear database (with confirmation prompt)
node scripts/clear-database.js

# Clear database automatically (skip confirmation)
node scripts/clear-database.js --force
```

### 3. Prerequisites

Before running the scripts, ensure:

1. **SQLite server is running:**
   ```bash
   cd server
   npm run dev
   ```

2. **Dependencies are installed:**
   ```bash
   npm install
   cd server && npm install
   ```

## 📈 Migration Process

The mock data loader follows a specific sequence to handle foreign key relationships:

1. **Step 1:** Migrate Alumni (base records)
2. **Step 2:** Migrate Users 
3. **Step 3:** Migrate Surveys
4. **Step 4:** Migrate Settings
5. **Step 5:** Migrate Employment (requires alumni_id references)
6. **Step 6:** Migrate Survey Responses (requires survey_id and alumni_id references)

## 🔧 Data Format

The mock data uses **camelCase** field names that are automatically converted to **snake_case** for the SQLite database:

```json
{
  "alumni": [
    {
      "id": "ALM20240001",
      "userId": "USR001",
      "nim": "2024001001",
      "namaLengkap": "John Doe",
      "programStudi": "Teknik Informatika",
      "fakultas": "Teknik",
      "tahunMasuk": 2020,
      "tahunLulus": 2024,
      "ipk": 3.75,
      "email": "<EMAIL>",
      "noTelepon": "081234567890",
      "alamat": "Jakarta",
      "statusVerifikasi": "verified",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## ⚠️ Important Notes

### Unique Constraints
- **NIM numbers** use format: `2024001001`, `2024001002`, etc.
- **Email addresses** use `.mock` suffix to avoid conflicts: `<EMAIL>`
- **User IDs** follow pattern: `USR001`, `USR002`, etc.

### Foreign Key Relationships
- Employment records reference `alumni_id`
- Survey responses reference both `survey_id` and `alumni_id`
- Alumni records can optionally reference `user_id`

### Data Consistency
- All timestamps use ISO 8601 format
- Salary values are in Indonesian Rupiah (IDR)
- Status fields use Indonesian terms: `bekerja`, `verified`, etc.

## 🐛 Troubleshooting

### Common Issues

1. **Server not running:**
   ```
   ❌ SQLite server is not running!
   💡 Please start the server first: cd server && npm run dev
   ```

2. **Unique constraint violations:**
   - Clear the database first: `node scripts/clear-database.js --force`
   - Or use different IDs in the mock data

3. **Foreign key constraint failures:**
   - Ensure alumni are migrated before employment
   - Ensure surveys are migrated before survey responses

### Checking Migration Status

After migration, check the results:

```bash
# The script will show final statistics like:
📊 Final database statistics:
   - alumni: 10 records
   - employment: 8 records  
   - surveys: 12 records
   - users: 10 records
   - survey_responses: 10 records
   - reports: 0 records
   - settings: 10 records
```

## 🎯 Use Cases

### Development
- Test UI components with realistic data
- Develop new features with sample records
- Debug data relationships and constraints

### Testing
- Unit tests with consistent sample data
- Integration tests with full dataset
- Performance testing with multiple records

### Demonstration
- Show application features to stakeholders
- Present realistic alumni tracking scenarios
- Demonstrate survey and reporting capabilities

## 🔄 Updating Mock Data

To modify the mock data:

1. Edit `data/mock-data.json`
2. Maintain the camelCase field naming convention
3. Ensure unique constraints are respected
4. Test the migration: `node scripts/load-mock-data.js --force`

## 📝 Next Steps

After loading mock data, you can:

1. **Start the frontend:** `npm run dev`
2. **Access the application:** `http://localhost:8081`
3. **Login with sample users** or create new accounts
4. **Explore features** with realistic data populated

---

For more information, see:
- [SQLite Migration Guide](./sqlite-migration-guide.md)
- [Excel Template Guide](./excel-template-guide.md)
