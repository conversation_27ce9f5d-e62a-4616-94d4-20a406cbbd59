
import React, { useState, useEffect } from 'react';
import { Users, Briefcase, TrendingUp, MapPin, DollarSign } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { dataManager } from '@/utils/dataManager';

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: React.ReactNode;
  trend?: string;
  trendUp?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, subtitle, icon, trend, trendUp }) => (
  <Card className="hover:shadow-lg transition-shadow">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
      <div className="h-8 w-8 university-gradient rounded-lg flex items-center justify-center text-white">
        {icon}
      </div>
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold text-gray-900">{value}</div>
      <div className="flex items-center space-x-2 mt-1">
        <p className="text-xs text-gray-500">{subtitle}</p>
        {trend && (
          <span className={`text-xs font-medium flex items-center ${
            trendUp ? 'text-green-600' : 'text-red-600'
          }`}>
            <TrendingUp className={`h-3 w-3 mr-1 ${!trendUp ? 'rotate-180' : ''}`} />
            {trend}
          </span>
        )}
      </div>
    </CardContent>
  </Card>
);

const DashboardStats = () => {
  const [stats, setStats] = useState([
    {
      title: 'Total Alumni',
      value: '0',
      subtitle: 'Alumni terdaftar',
      icon: <Users className="h-4 w-4" />,
      trend: '',
      trendUp: true
    },
    {
      title: 'Alumni Bekerja',
      value: '0',
      subtitle: '0% dari total alumni',
      icon: <Briefcase className="h-4 w-4" />,
      trend: '',
      trendUp: true
    },
    {
      title: 'Melanjutkan Studi',
      value: '0',
      subtitle: '0% melanjutkan S2/S3',
      icon: <img src="/logo-upi-manado.svg" alt="Logo UPI Manado" className="h-4 w-4" />,
      trend: '',
      trendUp: true
    },
    {
      title: 'Rata-rata Gaji',
      value: 'Rp 0',
      subtitle: 'Gaji pertama lulusan',
      icon: <DollarSign className="h-4 w-4" />,
      trend: '',
      trendUp: true
    },
    {
      title: 'Wirausaha',
      value: '0',
      subtitle: '0% alumni wirausaha',
      icon: <TrendingUp className="h-4 w-4" />,
      trend: '',
      trendUp: true
    },
    {
      title: 'Tersebar',
      value: '0 Kota',
      subtitle: 'Lokasi alumni tersebar',
      icon: <MapPin className="h-4 w-4" />
    }
  ]);

  useEffect(() => {
    const loadStats = () => {
      const alumni = dataManager.getAlumniData();
      const employment = dataManager.getEmploymentData();

      const totalAlumni = alumni.length;

      // Hitung alumni yang bekerja
      const alumniBekerja = employment.filter(emp => emp.statusPekerjaan === 'bekerja').length;
      const persentaseBekerja = totalAlumni > 0 ? ((alumniBekerja / totalAlumni) * 100).toFixed(1) : 0;

      // Hitung wirausaha
      const alumniWirausaha = employment.filter(emp => emp.statusPekerjaan === 'wirausaha').length;
      const persentaseWirausaha = totalAlumni > 0 ? ((alumniWirausaha / totalAlumni) * 100).toFixed(1) : 0;

      // Hitung rata-rata gaji
      const employmentWithSalary = employment.filter(emp => emp.gajiPertama && emp.gajiPertama > 0);
      const avgSalary = employmentWithSalary.length > 0
        ? employmentWithSalary.reduce((sum, emp) => sum + emp.gajiPertama, 0) / employmentWithSalary.length
        : 0;

      // Hitung kota unik dari alamat alumni
      const uniqueCities = new Set(
        alumni.map(alum => {
          const alamatParts = alum.alamat.split(',');
          return alamatParts[alamatParts.length - 1]?.trim() || 'Unknown';
        }).filter(city => city !== 'Unknown')
      );

      // Estimasi melanjutkan studi (alumni tanpa data employment)
      const alumniWithEmployment = new Set(employment.map(emp => emp.alumniId));
      const alumniMelanjutkanStudi = alumni.filter(alum => !alumniWithEmployment.has(alum.id)).length;
      const persentaseStudi = totalAlumni > 0 ? ((alumniMelanjutkanStudi / totalAlumni) * 100).toFixed(1) : 0;

      setStats([
        {
          title: 'Total Alumni',
          value: totalAlumni.toLocaleString('id-ID'),
          subtitle: 'Alumni terdaftar',
          icon: <Users className="h-4 w-4" />,
          trend: '',
          trendUp: true
        },
        {
          title: 'Alumni Bekerja',
          value: alumniBekerja.toLocaleString('id-ID'),
          subtitle: `${persentaseBekerja}% dari total alumni`,
          icon: <Briefcase className="h-4 w-4" />,
          trend: '',
          trendUp: true
        },
        {
          title: 'Melanjutkan Studi',
          value: alumniMelanjutkanStudi.toLocaleString('id-ID'),
          subtitle: `${persentaseStudi}% melanjutkan S2/S3`,
          icon: <img src="/logo-upi-manado.svg" alt="Logo UPI Manado" className="h-4 w-4" />,
          trend: '',
          trendUp: true
        },
        {
          title: 'Rata-rata Gaji',
          value: `Rp ${(avgSalary / 1000000).toFixed(1)} Jt`,
          subtitle: 'Gaji pertama lulusan',
          icon: <DollarSign className="h-4 w-4" />,
          trend: '',
          trendUp: true
        },
        {
          title: 'Wirausaha',
          value: alumniWirausaha.toLocaleString('id-ID'),
          subtitle: `${persentaseWirausaha}% alumni wirausaha`,
          icon: <TrendingUp className="h-4 w-4" />,
          trend: '',
          trendUp: true
        },
        {
          title: 'Tersebar',
          value: `${uniqueCities.size} Kota`,
          subtitle: 'Lokasi alumni tersebar',
          icon: <MapPin className="h-4 w-4" />
        }
      ]);
    };

    loadStats();
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export default DashboardStats;
