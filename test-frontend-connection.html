<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend-Backend Connection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Frontend-Backend Connection Test</h1>
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:3002/api';
        const resultsDiv = document.getElementById('results');

        function addResult(title, status, message, data = null) {
            const div = document.createElement('div');
            div.className = 'test-item';
            
            let content = `<h3>${title}</h3><p class="${status}">${message}</p>`;
            if (data) {
                content += `<pre style="background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            div.innerHTML = content;
            resultsDiv.appendChild(div);
        }

        async function testConnection() {
            addResult('Starting Tests', 'loading', 'Testing frontend-backend connection...');

            // Test 1: Health Check
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const result = await response.json();
                
                if (result.success) {
                    addResult('✅ Health Check', 'success', 'Backend server is running correctly', result);
                } else {
                    addResult('❌ Health Check', 'error', 'Backend server responded but with error', result);
                }
            } catch (error) {
                addResult('❌ Health Check', 'error', `Cannot connect to backend: ${error.message}`);
                return; // Stop if we can't connect
            }

            // Test 2: Alumni API
            try {
                const response = await fetch(`${API_BASE_URL}/alumni`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    const realAlumni = result.data.filter(a => 
                        a.nama_lengkap.includes('AGUSTINA') || 
                        a.nama_lengkap.includes('AGUSTINUS') || 
                        a.nama_lengkap.includes('AHMAD SHODIQUN')
                    );
                    
                    addResult('✅ Alumni API', 'success', 
                        `Alumni API working. Total: ${result.data.length}, Real alumni: ${realAlumni.length}`,
                        { 
                            total: result.data.length,
                            realAlumni: realAlumni.map(a => ({ nim: a.nim, nama: a.nama_lengkap }))
                        }
                    );
                } else {
                    addResult('❌ Alumni API', 'error', 'Alumni API failed', result);
                }
            } catch (error) {
                addResult('❌ Alumni API', 'error', `Alumni API error: ${error.message}`);
            }

            // Test 3: Users API
            try {
                const response = await fetch(`${API_BASE_URL}/users`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    const realAlumniUsers = result.data.filter(u => 
                        u.username === '185420110' || 
                        u.username === '01210014' || 
                        u.username === '195420110' || 
                        u.username === '9210058'
                    );
                    
                    addResult('✅ Users API', 'success', 
                        `Users API working. Total: ${result.data.length}, Real alumni users: ${realAlumniUsers.length}`,
                        { 
                            total: result.data.length,
                            realAlumniUsers: realAlumniUsers.map(u => ({ username: u.username, email: u.email }))
                        }
                    );
                } else {
                    addResult('❌ Users API', 'error', 'Users API failed', result);
                }
            } catch (error) {
                addResult('❌ Users API', 'error', `Users API error: ${error.message}`);
            }

            // Test 4: CORS Check
            try {
                const response = await fetch(`${API_BASE_URL}/alumni`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'http://localhost:8084',
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                addResult('✅ CORS Check', 'success', 'CORS is properly configured');
            } catch (error) {
                addResult('⚠️ CORS Check', 'warning', `CORS might have issues: ${error.message}`);
            }

            // Test 5: Authentication Test
            try {
                const response = await fetch(`${API_BASE_URL}/users/authenticate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '185420110'
                    }),
                });

                const result = await response.json();
                
                if (result.success) {
                    addResult('✅ Authentication Test', 'success', 
                        'Real alumni authentication working',
                        { user: result.data.nama_lengkap, role: result.data.role }
                    );
                } else {
                    addResult('❌ Authentication Test', 'error', 'Authentication failed', result);
                }
            } catch (error) {
                addResult('❌ Authentication Test', 'error', `Authentication error: ${error.message}`);
            }

            addResult('🎉 Tests Completed', 'success', 'All connection tests have been completed. Check results above.');
        }

        // Run tests when page loads
        window.onload = testConnection;
    </script>
</body>
</html>
