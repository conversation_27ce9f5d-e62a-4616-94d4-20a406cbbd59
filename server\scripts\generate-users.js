// Script to generate users from alumni data
const axios = require('axios');

const API_URL = 'http://localhost:3002/api';

async function generateUsers() {
  try {
    console.log('Generating users from alumni data...');
    
    const response = await axios.post(`${API_URL}/users/generate-from-alumni`);
    
    console.log('Response:', response.data);
    
    if (response.data.success) {
      console.log(`Successfully created ${response.data.data.created} user accounts`);
      if (response.data.data.skipped > 0) {
        console.log(`Skipped ${response.data.data.skipped} alumni records`);
      }
    } else {
      console.error('Failed to generate users:', response.data.message);
    }
  } catch (error) {
    console.error('Error generating users:', error.response?.data || error.message);
  }
}

generateUsers();
