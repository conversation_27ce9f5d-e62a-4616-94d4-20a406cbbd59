// SQLite Data Manager - replaces localStorage with API calls

import apiClient from './apiClient';
import { 
  AlumniData, 
  EmploymentData, 
  SurveyData, 
  SurveyResponse, 
  UserData, 
  ReportData, 
  SettingsData 
} from './dataManager';

class SQLiteDataManager {
  private cache: { [key: string]: any } = {};
  private cacheExpiry: { [key: string]: number } = {};
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Cache management
  private isCacheValid(key: string): boolean {
    return this.cache[key] && this.cacheExpiry[key] > Date.now();
  }

  private setCache(key: string, data: any): void {
    this.cache[key] = data;
    this.cacheExpiry[key] = Date.now() + this.CACHE_DURATION;
  }

  private clearCache(key?: string): void {
    if (key) {
      delete this.cache[key];
      delete this.cacheExpiry[key];
    } else {
      this.cache = {};
      this.cacheExpiry = {};
    }
  }

  // Alumni data management
  async getAlumniData(): Promise<AlumniData[]> {
    const cacheKey = 'alumni';
    if (this.isCacheValid(cacheKey)) {
      return this.cache[cacheKey];
    }

    try {
      const response = await apiClient.getAlumni();
      if (response.success && response.data) {
        // Convert database format to frontend format
        const alumni = response.data.map((item: any) => ({
          id: item.id,
          userId: item.user_id,
          nim: item.nim,
          namaLengkap: item.nama_lengkap,
          programStudi: item.program_studi,
          fakultas: item.fakultas,
          tahunMasuk: item.tahun_masuk,
          tahunLulus: item.tahun_lulus,
          ipk: item.ipk,
          email: item.email,
          noTelepon: item.no_telepon,
          alamat: item.alamat,
          statusVerifikasi: item.status_verifikasi,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        }));
        
        this.setCache(cacheKey, alumni);
        return alumni;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch alumni data:', error);
      return [];
    }
  }

  async getAlumniById(id: string): Promise<AlumniData | null> {
    try {
      const response = await apiClient.getAlumniById(id);
      if (response.success && response.data) {
        const item = response.data;
        return {
          id: item.id,
          userId: item.user_id,
          nim: item.nim,
          namaLengkap: item.nama_lengkap,
          programStudi: item.program_studi,
          fakultas: item.fakultas,
          tahunMasuk: item.tahun_masuk,
          tahunLulus: item.tahun_lulus,
          ipk: item.ipk,
          email: item.email,
          noTelepon: item.no_telepon,
          alamat: item.alamat,
          statusVerifikasi: item.status_verifikasi,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to fetch alumni by ID:', error);
      return null;
    }
  }

  async addAlumni(alumni: Omit<AlumniData, 'id' | 'createdAt' | 'updatedAt'>): Promise<AlumniData> {
    try {
      // Convert frontend format to database format
      const data = {
        user_id: alumni.userId,
        nim: alumni.nim,
        nama_lengkap: alumni.namaLengkap,
        program_studi: alumni.programStudi,
        fakultas: alumni.fakultas,
        tahun_masuk: alumni.tahunMasuk,
        tahun_lulus: alumni.tahunLulus,
        ipk: alumni.ipk,
        email: alumni.email,
        no_telepon: alumni.noTelepon,
        alamat: alumni.alamat,
        status_verifikasi: alumni.statusVerifikasi
      };

      const response = await apiClient.createAlumni(data);
      if (response.success && response.data) {
        this.clearCache('alumni');
        const item = response.data;
        return {
          id: item.id,
          userId: item.user_id,
          nim: item.nim,
          namaLengkap: item.nama_lengkap,
          programStudi: item.program_studi,
          fakultas: item.fakultas,
          tahunMasuk: item.tahun_masuk,
          tahunLulus: item.tahun_lulus,
          ipk: item.ipk,
          email: item.email,
          noTelepon: item.no_telepon,
          alamat: item.alamat,
          statusVerifikasi: item.status_verifikasi,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        };
      }
      throw new Error('Failed to create alumni');
    } catch (error) {
      console.error('Failed to add alumni:', error);
      throw error;
    }
  }

  async updateAlumni(id: string, updates: Partial<Omit<AlumniData, 'id' | 'createdAt'>>): Promise<AlumniData | null> {
    try {
      // Convert frontend format to database format
      const data: any = {};
      if (updates.userId !== undefined) data.user_id = updates.userId;
      if (updates.nim !== undefined) data.nim = updates.nim;
      if (updates.namaLengkap !== undefined) data.nama_lengkap = updates.namaLengkap;
      if (updates.programStudi !== undefined) data.program_studi = updates.programStudi;
      if (updates.fakultas !== undefined) data.fakultas = updates.fakultas;
      if (updates.tahunMasuk !== undefined) data.tahun_masuk = updates.tahunMasuk;
      if (updates.tahunLulus !== undefined) data.tahun_lulus = updates.tahunLulus;
      if (updates.ipk !== undefined) data.ipk = updates.ipk;
      if (updates.email !== undefined) data.email = updates.email;
      if (updates.noTelepon !== undefined) data.no_telepon = updates.noTelepon;
      if (updates.alamat !== undefined) data.alamat = updates.alamat;
      if (updates.statusVerifikasi !== undefined) data.status_verifikasi = updates.statusVerifikasi;

      const response = await apiClient.updateAlumni(id, data);
      if (response.success && response.data) {
        this.clearCache('alumni');
        const item = response.data;
        return {
          id: item.id,
          userId: item.user_id,
          nim: item.nim,
          namaLengkap: item.nama_lengkap,
          programStudi: item.program_studi,
          fakultas: item.fakultas,
          tahunMasuk: item.tahun_masuk,
          tahunLulus: item.tahun_lulus,
          ipk: item.ipk,
          email: item.email,
          noTelepon: item.no_telepon,
          alamat: item.alamat,
          statusVerifikasi: item.status_verifikasi,
          createdAt: item.created_at,
          updatedAt: item.updated_at
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to update alumni:', error);
      return null;
    }
  }

  async deleteAlumni(id: string): Promise<boolean> {
    try {
      const response = await apiClient.deleteAlumni(id);
      if (response.success) {
        this.clearCache('alumni');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to delete alumni:', error);
      return false;
    }
  }

  // Employment data management
  async getEmploymentData(): Promise<EmploymentData[]> {
    const cacheKey = 'employment';
    if (this.isCacheValid(cacheKey)) {
      return this.cache[cacheKey];
    }

    try {
      const response = await apiClient.getEmployment();
      if (response.success && response.data) {
        const employment = response.data.map((item: any) => ({
          id: item.id,
          alumniId: item.alumni_id,
          namaPerusahaan: item.nama_perusahaan,
          posisiJabatan: item.posisi_jabatan,
          jenisUsaha: item.jenis_usaha,
          gajiPertama: item.gaji_pertama,
          gajiSaatIni: item.gaji_saat_ini,
          tanggalMulaiKerja: item.tanggal_mulai_kerja,
          statusPekerjaan: item.status_pekerjaan,
          relevansiPekerjaan: item.relevansi_pekerjaan
        }));
        
        this.setCache(cacheKey, employment);
        return employment;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch employment data:', error);
      return [];
    }
  }

  async addEmployment(employment: Omit<EmploymentData, 'id'>): Promise<EmploymentData> {
    try {
      const data = {
        alumni_id: employment.alumniId,
        nama_perusahaan: employment.namaPerusahaan,
        posisi_jabatan: employment.posisiJabatan,
        jenis_usaha: employment.jenisUsaha,
        gaji_pertama: employment.gajiPertama,
        gaji_saat_ini: employment.gajiSaatIni,
        tanggal_mulai_kerja: employment.tanggalMulaiKerja,
        status_pekerjaan: employment.statusPekerjaan,
        relevansi_pekerjaan: employment.relevansiPekerjaan
      };

      const response = await apiClient.createEmployment(data);
      if (response.success && response.data) {
        this.clearCache('employment');
        const item = response.data;
        return {
          id: item.id,
          alumniId: item.alumni_id,
          namaPerusahaan: item.nama_perusahaan,
          posisiJabatan: item.posisi_jabatan,
          jenisUsaha: item.jenis_usaha,
          gajiPertama: item.gaji_pertama,
          gajiSaatIni: item.gaji_saat_ini,
          tanggalMulaiKerja: item.tanggal_mulai_kerja,
          statusPekerjaan: item.status_pekerjaan,
          relevansiPekerjaan: item.relevansi_pekerjaan
        };
      }
      throw new Error('Failed to create employment record');
    } catch (error) {
      console.error('Failed to add employment:', error);
      throw error;
    }
  }

  // User data management
  async getUserData(): Promise<UserData[]> {
    const cacheKey = 'users';
    if (this.isCacheValid(cacheKey)) {
      return this.cache[cacheKey];
    }

    try {
      const response = await apiClient.getUsers();
      if (response.success && response.data) {
        const users = response.data.map((item: any) => ({
          id: item.id,
          username: item.username,
          email: item.email,
          role: item.role,
          namaLengkap: item.nama_lengkap,
          isActive: item.is_active,
          createdAt: item.created_at,
          lastLogin: item.last_login
        }));
        
        this.setCache(cacheKey, users);
        return users;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch user data:', error);
      return [];
    }
  }

  async getUserById(id: string): Promise<UserData | null> {
    try {
      const response = await apiClient.getUserById(id);
      if (response.success && response.data) {
        const item = response.data;
        return {
          id: item.id,
          username: item.username,
          email: item.email,
          role: item.role,
          namaLengkap: item.nama_lengkap,
          isActive: item.is_active,
          createdAt: item.created_at,
          lastLogin: item.last_login
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to fetch user by ID:', error);
      return null;
    }
  }

  async addUser(user: Omit<UserData, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserData> {
    try {
      const userData = {
        username: user.username,
        email: user.email,
        role: user.role,
        nama_lengkap: user.namaLengkap,
        is_active: user.isActive !== undefined ? user.isActive : true // Default to active
      };

      const response = await apiClient.createUser(userData);
      if (response.success && response.data) {
        this.clearCache('users');
        const item = response.data;
        return {
          id: item.id,
          username: item.username,
          email: item.email,
          role: item.role,
          namaLengkap: item.nama_lengkap,
          isActive: item.is_active,
          createdAt: item.created_at,
          lastLogin: item.last_login
        };
      }
      throw new Error('Failed to create user');
    } catch (error) {
      console.error('Failed to add user:', error);
      throw error;
    }
  }

  async updateUser(id: string, updates: Partial<Omit<UserData, 'id' | 'createdAt'>>): Promise<UserData | null> {
    try {
      const updateData: any = {};
      if (updates.username !== undefined) updateData.username = updates.username;
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.role !== undefined) updateData.role = updates.role;
      if (updates.namaLengkap !== undefined) updateData.nama_lengkap = updates.namaLengkap;
      if (updates.isActive !== undefined) updateData.is_active = updates.isActive;

      const response = await apiClient.updateUser(id, updateData);
      if (response.success && response.data) {
        this.clearCache('users');
        const item = response.data;
        return {
          id: item.id,
          username: item.username,
          email: item.email,
          role: item.role,
          namaLengkap: item.nama_lengkap,
          isActive: item.is_active,
          createdAt: item.created_at,
          lastLogin: item.last_login
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      const response = await apiClient.deleteUser(id);
      if (response.success) {
        this.clearCache('users');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Failed to delete user:', error);
      return false;
    }
  }

  async generateUsersFromAlumni(): Promise<{ created: number; skipped: number; skippedAlumni: any[] }> {
    try {
      const response = await apiClient.generateUsersFromAlumni();
      if (response.success && response.data) {
        this.clearCache('users');
        return response.data;
      }
      throw new Error('Failed to generate users from alumni');
    } catch (error) {
      console.error('Failed to generate users from alumni:', error);
      throw error;
    }
  }

  // Settings data management
  async getSettingsData(): Promise<SettingsData[]> {
    try {
      const response = await apiClient.getSettings();
      if (response.success && response.data) {
        // Convert object format to array format for compatibility
        return Object.entries(response.data).map(([key, value]) => ({
          id: `setting-${key}`,
          key,
          value,
          description: '',
          updatedAt: new Date().toISOString()
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch settings data:', error);
      return [];
    }
  }

  async updateSetting(key: string, value: any): Promise<SettingsData> {
    try {
      const response = await apiClient.updateSetting(key, value);
      if (response.success && response.data) {
        return {
          id: `setting-${key}`,
          key: response.data.key,
          value: response.data.value,
          description: response.data.description || '',
          updatedAt: response.data.updated_at
        };
      }
      throw new Error('Failed to update setting');
    } catch (error) {
      console.error('Failed to update setting:', error);
      throw error;
    }
  }

  // Survey data management (basic implementation)
  async getSurveyData(): Promise<SurveyData[]> {
    try {
      const response = await apiClient.getSurveys();
      if (response.success && response.data) {
        return response.data.map((item: any) => ({
          id: item.id,
          judul: item.judul,
          deskripsi: item.deskripsi,
          tanggalMulai: item.tanggal_mulai,
          tanggalSelesai: item.tanggal_selesai,
          status: item.status,
          targetAlumni: item.target_alumni || [],
          questions: item.questions || [],
          responses: []
        }));
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch survey data:', error);
      return [];
    }
  }

  async getSurveyById(id: string): Promise<SurveyData | null> {
    try {
      const response = await apiClient.getSurveyById(id);
      if (response.success && response.data) {
        const item = response.data;
        return {
          id: item.id,
          judul: item.judul,
          deskripsi: item.deskripsi,
          tanggalMulai: item.tanggal_mulai,
          tanggalSelesai: item.tanggal_selesai,
          status: item.status,
          targetAlumni: item.target_alumni || [],
          questions: item.questions || [],
          responses: []
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to fetch survey by ID:', error);
      return null;
    }
  }

  async addSurvey(survey: Omit<SurveyData, 'id' | 'createdAt' | 'updatedAt'>): Promise<SurveyData> {
    try {
      const surveyData = {
        judul: survey.judul,
        deskripsi: survey.deskripsi,
        tanggal_mulai: survey.tanggalMulai,
        tanggal_selesai: survey.tanggalSelesai,
        status: survey.status,
        target_alumni: survey.targetAlumni,
        questions: survey.questions
      };

      const response = await apiClient.createSurvey(surveyData);
      if (response.success && response.data) {
        const item = response.data;
        return {
          id: item.id,
          judul: item.judul,
          deskripsi: item.deskripsi,
          tanggalMulai: item.tanggal_mulai,
          tanggalSelesai: item.tanggal_selesai,
          status: item.status,
          targetAlumni: item.target_alumni || [],
          questions: item.questions || [],
          responses: []
        };
      }
      throw new Error('Failed to create survey');
    } catch (error) {
      console.error('Failed to add survey:', error);
      throw error;
    }
  }

  async updateSurvey(id: string, updates: Partial<Omit<SurveyData, 'id' | 'createdAt'>>): Promise<SurveyData | null> {
    try {
      const updateData: any = {};
      if (updates.judul !== undefined) updateData.judul = updates.judul;
      if (updates.deskripsi !== undefined) updateData.deskripsi = updates.deskripsi;
      if (updates.tanggalMulai !== undefined) updateData.tanggal_mulai = updates.tanggalMulai;
      if (updates.tanggalSelesai !== undefined) updateData.tanggal_selesai = updates.tanggalSelesai;
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.targetAlumni !== undefined) updateData.target_alumni = updates.targetAlumni;
      if (updates.questions !== undefined) updateData.questions = updates.questions;

      const response = await apiClient.updateSurvey(id, updateData);
      if (response.success && response.data) {
        const item = response.data;
        return {
          id: item.id,
          judul: item.judul,
          deskripsi: item.deskripsi,
          tanggalMulai: item.tanggal_mulai,
          tanggalSelesai: item.tanggal_selesai,
          status: item.status,
          targetAlumni: item.target_alumni || [],
          questions: item.questions || [],
          responses: []
        };
      }
      return null;
    } catch (error) {
      console.error('Failed to update survey:', error);
      throw error;
    }
  }

  async deleteSurvey(id: string): Promise<boolean> {
    try {
      const response = await apiClient.deleteSurvey(id);
      return response.success;
    } catch (error) {
      console.error('Failed to delete survey:', error);
      return false;
    }
  }

  async getSurveyResponses(): Promise<SurveyResponse[]> {
    // This would need to be implemented based on specific survey ID
    // For now, return empty array
    return [];
  }

  // Migration helper
  async migrateFromLocalStorage(): Promise<void> {
    try {
      // Get data from localStorage
      const localData = {
        alumni: JSON.parse(localStorage.getItem('tracer_alumni') || '[]'),
        employment: JSON.parse(localStorage.getItem('tracer_employment') || '[]'),
        users: JSON.parse(localStorage.getItem('tracer_users') || '[]'),
        surveys: JSON.parse(localStorage.getItem('tracer_surveys') || '[]'),
        settings: JSON.parse(localStorage.getItem('tracer_settings') || '[]'),
        survey_responses: JSON.parse(localStorage.getItem('tracer_survey_responses') || '[]')
      };

      // Migrate to SQLite
      const response = await apiClient.migrateFromJson(localData);
      if (response.success) {
        console.log('Migration successful:', response.data);
        // Clear cache to force refresh
        this.clearCache();
      } else {
        throw new Error(response.message || 'Migration failed');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  // Health check
  async isServerAvailable(): Promise<boolean> {
    try {
      const response = await apiClient.healthCheck();
      return response.success;
    } catch (error) {
      return false;
    }
  }

  // Compatibility methods for existing code
  saveAlumniData(alumni: AlumniData[]): void {
    // This is now handled by individual API calls
    console.warn('saveAlumniData is deprecated with SQLite backend');
  }

  saveEmploymentData(employment: EmploymentData[]): void {
    console.warn('saveEmploymentData is deprecated with SQLite backend');
  }

  saveUserData(users: UserData[]): void {
    console.warn('saveUserData is deprecated with SQLite backend');
  }

  saveSurveyData(surveys: SurveyData[]): void {
    console.warn('saveSurveyData is deprecated with SQLite backend');
  }

  saveSettingsData(settings: SettingsData[]): void {
    console.warn('saveSettingsData is deprecated with SQLite backend');
  }

  clearAllData(): void {
    console.warn('clearAllData should be called through API');
  }

  isDatabaseEmpty(): boolean {
    console.warn('isDatabaseEmpty should be checked through API');
    return false;
  }

  initializeSampleData(): void {
    console.warn('initializeSampleData is not needed with SQLite backend');
  }
}

export const sqliteDataManager = new SQLiteDataManager();
export default sqliteDataManager;
