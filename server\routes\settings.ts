import express from 'express';
import DatabaseConnection from '../database/connection';
import { SettingsData } from '../models/types';

const router = express.Router();
const db = DatabaseConnection.getInstance().getDatabase();

// Helper function to generate settings ID
const generateSettingsId = (): string => {
  return `SET${Date.now()}${Math.random().toString(36).substr(2, 5)}`;
};

// GET /api/settings - Get all settings
router.get('/', (req, res) => {
  try {
    const stmt = db.prepare('SELECT * FROM settings ORDER BY key');
    const settings = stmt.all() as SettingsData[];
    
    // Parse JSON values and convert to key-value object
    const settingsObject: { [key: string]: any } = {};
    settings.forEach(setting => {
      try {
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    res.json({
      success: true,
      data: settingsObject,
      raw: settings // Include raw data for admin purposes
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/settings/:key - Get specific setting
router.get('/:key', (req, res) => {
  try {
    const stmt = db.prepare('SELECT * FROM settings WHERE key = ?');
    const setting = stmt.get(req.params.key) as SettingsData | undefined;
    
    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }

    let value;
    try {
      value = JSON.parse(setting.value);
    } catch {
      value = setting.value;
    }

    res.json({
      success: true,
      data: {
        key: setting.key,
        value,
        description: setting.description,
        updated_at: setting.updated_at
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch setting',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/settings/:key - Update or create setting
router.put('/:key', (req, res) => {
  try {
    const { value, description } = req.body;

    if (value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'value is required'
      });
    }

    const now = new Date().toISOString();
    const jsonValue = JSON.stringify(value);

    // Check if setting exists
    const existing = db.prepare('SELECT * FROM settings WHERE key = ?').get(req.params.key) as SettingsData | undefined;

    if (existing) {
      // Update existing setting
      const stmt = db.prepare(`
        UPDATE settings 
        SET value = ?, description = COALESCE(?, description), updated_at = ?
        WHERE key = ?
      `);
      
      const result = stmt.run(jsonValue, description, now, req.params.key);
      
      if (result.changes === 0) {
        throw new Error('Failed to update setting');
      }
    } else {
      // Create new setting
      const id = generateSettingsId();
      const stmt = db.prepare(`
        INSERT INTO settings (id, key, value, description, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `);
      
      const result = stmt.run(id, req.params.key, jsonValue, description || '', now);
      
      if (result.changes === 0) {
        throw new Error('Failed to create setting');
      }
    }

    // Return updated setting
    const updated = db.prepare('SELECT * FROM settings WHERE key = ?').get(req.params.key) as SettingsData;
    let parsedValue;
    try {
      parsedValue = JSON.parse(updated.value);
    } catch {
      parsedValue = updated.value;
    }

    res.json({
      success: true,
      data: {
        key: updated.key,
        value: parsedValue,
        description: updated.description,
        updated_at: updated.updated_at
      },
      message: existing ? 'Setting updated successfully' : 'Setting created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update setting',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/settings/bulk - Bulk update settings
router.post('/bulk', (req, res) => {
  try {
    const settings: { [key: string]: any } = req.body;

    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Invalid settings object'
      });
    }

    const now = new Date().toISOString();
    let updatedCount = 0;
    let createdCount = 0;

    // Use transaction for bulk update
    const updateSettings = db.transaction(() => {
      for (const [key, value] of Object.entries(settings)) {
        const jsonValue = JSON.stringify(value);
        
        // Check if setting exists
        const existing = db.prepare('SELECT * FROM settings WHERE key = ?').get(key);
        
        if (existing) {
          // Update existing
          const stmt = db.prepare('UPDATE settings SET value = ?, updated_at = ? WHERE key = ?');
          stmt.run(jsonValue, now, key);
          updatedCount++;
        } else {
          // Create new
          const id = generateSettingsId();
          const stmt = db.prepare('INSERT INTO settings (id, key, value, description, updated_at) VALUES (?, ?, ?, ?, ?)');
          stmt.run(id, key, jsonValue, '', now);
          createdCount++;
        }
      }
    });

    updateSettings();

    res.json({
      success: true,
      message: `Settings updated successfully. Updated: ${updatedCount}, Created: ${createdCount}`,
      data: {
        updated: updatedCount,
        created: createdCount
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to bulk update settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/settings/:key - Delete setting
router.delete('/:key', (req, res) => {
  try {
    const stmt = db.prepare('DELETE FROM settings WHERE key = ?');
    const result = stmt.run(req.params.key);
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }

    res.json({
      success: true,
      message: 'Setting deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete setting',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/settings/reset - Reset to default settings
router.post('/reset', (req, res) => {
  try {
    // Clear all settings
    db.prepare('DELETE FROM settings').run();
    
    // Insert default settings
    const defaultSettings = [
      { key: 'university_name', value: '"Universitas Alumni Connect"', description: 'Nama universitas' },
      { key: 'contact_email', value: '"<EMAIL>"', description: 'Email kontak' },
      { key: 'contact_phone', value: '"+62 21 1234567"', description: 'Nomor telepon kontak' },
      { key: 'address', value: '"Jl. Pendidikan No. 123, Jakarta"', description: 'Alamat universitas' },
      { key: 'survey_reminder_days', value: '7', description: 'Hari pengingat survey' },
      { key: 'email_notifications', value: 'true', description: 'Notifikasi email' },
      { key: 'auto_verification', value: 'false', description: 'Verifikasi otomatis' },
      { key: 'max_file_size_mb', value: '10', description: 'Ukuran maksimal file (MB)' },
      { key: 'session_duration_minutes', value: '60', description: 'Durasi sesi (menit)' },
      { key: 'theme', value: '"light"', description: 'Tema aplikasi' }
    ];

    const stmt = db.prepare('INSERT INTO settings (id, key, value, description, updated_at) VALUES (?, ?, ?, ?, ?)');
    const now = new Date().toISOString();

    defaultSettings.forEach((setting, index) => {
      const id = `set-${(index + 1).toString().padStart(3, '0')}`;
      stmt.run(id, setting.key, setting.value, setting.description, now);
    });

    res.json({
      success: true,
      message: 'Settings reset to default values',
      data: { count: defaultSettings.length }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to reset settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
