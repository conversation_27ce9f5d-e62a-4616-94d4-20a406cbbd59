
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  FileText,
  BarChart3,
  Settings,
  HelpCircle,
  ChevronRight,
  GraduationCap,
  UserCheck,
  ClipboardList,
  Building
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  path: string;
  badge?: string;
  submenu?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    icon: <LayoutDashboard className="h-5 w-5" />,
    label: 'Dashboard',
    path: '/dashboard'
  },
  {
    icon: <Users className="h-5 w-5" />,
    label: 'Manajemen Alumni',
    path: '/alumni',
    submenu: [
      { icon: <UserCheck className="h-4 w-4" />, label: 'Daftar Alumni', path: '/alumni/list' },
      { icon: <GraduationCap className="h-4 w-4" />, label: 'Verifikasi Data', path: '/alumni/verification' }
    ]
  },
  {
    icon: <Building className="h-5 w-5" />,
    label: '<PERSON> Pekerjaan',
    path: '/employment'
  },
  {
    icon: <ClipboardList className="h-5 w-5" />,
    label: 'Survey & Kuesioner',
    path: '/surveys',
    badge: '3'
  },
  {
    icon: <BarChart3 className="h-5 w-5" />,
    label: 'Laporan & Analytics',
    path: '/reports'
  },
  {
    icon: <FileText className="h-5 w-5" />,
    label: 'Data Export',
    path: '/export'
  },
  {
    icon: <Settings className="h-5 w-5" />,
    label: 'Pengaturan',
    path: '/settings'
  },
  {
    icon: <Users className="h-5 w-5" />,
    label: 'Manajemen Pengguna',
    path: '/users'
  }
];

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpand = (path: string) => {
    setExpandedItems(prev => 
      prev.includes(path) 
        ? prev.filter(item => item !== path)
        : [...prev, path]
    );
  };

  const isExpanded = (path: string) => expandedItems.includes(path);

  const isActive = (path: string) => {
    if (path === '/dashboard' && (location.pathname === '/' || location.pathname === '/dashboard')) {
      return true;
    }
    return location.pathname === path;
  };

  const handleNavigation = (path: string, hasSubmenu: boolean) => {
    if (hasSubmenu) {
      toggleExpand(path);
    } else {
      navigate(path);
    }
  };

  return (
    <div className="h-full bg-white border-r border-gray-200 w-64 fixed left-0 top-16 z-40">
      <div className="flex flex-col h-full">
        <nav className="flex-1 px-4 py-6 space-y-2">
          {menuItems.map((item) => (
            <div key={item.path}>
              <Button
                variant={isActive(item.path) ? "default" : "ghost"}
                className={`w-full justify-between h-auto p-3 ${
                  isActive(item.path)
                    ? "university-gradient text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                }`}
                onClick={() => handleNavigation(item.path, !!item.submenu)}
              >
                <div className="flex items-center space-x-3">
                  {item.icon}
                  <span className="font-medium">{item.label}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {item.badge && (
                    <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                      {item.badge}
                    </span>
                  )}
                  {item.submenu && (
                    <ChevronRight 
                      className={`h-4 w-4 transition-transform ${
                        isExpanded(item.path) ? 'rotate-90' : ''
                      }`} 
                    />
                  )}
                </div>
              </Button>
              
              {item.submenu && isExpanded(item.path) && (
                <div className="ml-4 mt-2 space-y-1">
                  {item.submenu.map((subItem) => (
                    <Button
                      key={subItem.path}
                      variant="ghost"
                      className={`w-full justify-start h-auto p-2 text-sm ${
                        isActive(subItem.path)
                          ? "bg-blue-50 text-blue-700 font-medium"
                          : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                      }`}
                      onClick={() => navigate(subItem.path)}
                    >
                      <div className="flex items-center space-x-2">
                        {subItem.icon}
                        <span>{subItem.label}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
        
        <div className="px-4 py-4 border-t border-gray-200">
          <Button variant="ghost" className="w-full justify-start text-gray-600 hover:text-gray-900">
            <HelpCircle className="h-5 w-5 mr-3" />
            Bantuan & Support
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
