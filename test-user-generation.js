// Test script to verify User Generation from Alumni
const API_BASE_URL = 'http://localhost:3002/api';

async function testUserGeneration() {
  console.log('🧪 Testing User Generation from Alumni...\n');

  try {
    // 1. Check current users
    console.log('1️⃣ Checking current users...');
    const usersResponse = await fetch(`${API_BASE_URL}/users`);
    const usersResult = await usersResponse.json();
    
    if (usersResult.success) {
      console.log('✅ Current users:', usersResult.data.length);
      console.log('Users:', usersResult.data.map(u => ({ email: u.email, role: u.role })));
    }

    // 2. Check alumni data
    console.log('\n2️⃣ Checking alumni data...');
    const alumniResponse = await fetch(`${API_BASE_URL}/alumni`);
    const alumniResult = await alumniResponse.json();
    
    if (alumniResult.success) {
      const alumniWithEmail = alumniResult.data.filter(a => a.email && a.email.trim() !== '');
      console.log('✅ Total alumni:', alumniResult.data.length);
      console.log('✅ Alumni with email:', alumniWithEmail.length);
      console.log('Sample alumni:', alumniWithEmail.slice(0, 3).map(a => ({ 
        nim: a.nim, 
        nama_lengkap: a.nama_lengkap, 
        email: a.email 
      })));
    }

    // 3. Generate users from alumni
    console.log('\n3️⃣ Generating users from alumni...');
    const generateResponse = await fetch(`${API_BASE_URL}/users/generate-from-alumni`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const generateResult = await generateResponse.json();
    
    if (generateResult.success) {
      console.log('✅ Generation successful!');
      console.log('📊 Results:');
      console.log(`   - Created: ${generateResult.data.created} users`);
      console.log(`   - Skipped: ${generateResult.data.skipped} alumni`);
      
      if (generateResult.data.skippedAlumni && generateResult.data.skippedAlumni.length > 0) {
        console.log('\n⚠️ Skipped alumni:');
        generateResult.data.skippedAlumni.forEach((alumni, index) => {
          console.log(`   ${index + 1}. ${alumni.nim} - ${alumni.nama_lengkap} (${alumni.reason})`);
        });
      }
    } else {
      console.log('❌ Generation failed:', generateResult.message);
    }

    // 4. Check users after generation
    console.log('\n4️⃣ Checking users after generation...');
    const newUsersResponse = await fetch(`${API_BASE_URL}/users`);
    const newUsersResult = await newUsersResponse.json();
    
    if (newUsersResult.success) {
      const alumniUsers = newUsersResult.data.filter(u => u.role === 'alumni');
      console.log('✅ Total users after generation:', newUsersResult.data.length);
      console.log('✅ Alumni users:', alumniUsers.length);
      console.log('Sample alumni users:', alumniUsers.slice(0, 3).map(u => ({ 
        username: u.username, 
        email: u.email, 
        role: u.role 
      })));
    }

    // 5. Test authentication with NIM as password
    console.log('\n5️⃣ Testing authentication with NIM as password...');
    
    // Get a sample alumni user
    const sampleAlumniUser = newUsersResult.data.find(u => u.role === 'alumni');
    if (sampleAlumniUser) {
      // Get the corresponding alumni data to find the NIM
      const alumniData = alumniResult.data.find(a => a.email === sampleAlumniUser.email);
      
      if (alumniData) {
        console.log(`Testing login for: ${sampleAlumniUser.email} with NIM: ${alumniData.nim}`);
        
        const authResponse = await fetch(`${API_BASE_URL}/users/authenticate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: sampleAlumniUser.email,
            password: alumniData.nim
          }),
        });

        const authResult = await authResponse.json();
        
        if (authResult.success) {
          console.log('✅ Authentication successful!');
          console.log('User data:', {
            id: authResult.data.id,
            email: authResult.data.email,
            role: authResult.data.role,
            nama_lengkap: authResult.data.nama_lengkap
          });
        } else {
          console.log('❌ Authentication failed:', authResult.message);
        }
      }
    }

    console.log('\n🎉 User generation test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testUserGeneration();
