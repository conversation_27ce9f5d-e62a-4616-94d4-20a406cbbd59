{"name": "tracer-alumni-server", "version": "1.0.0", "description": "SQLite backend server for Tracer Alumni system", "main": "app.ts", "scripts": {"start": "tsx app.ts", "dev": "tsx watch app.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"better-sqlite3": "^9.2.2", "express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"@types/better-sqlite3": "^7.6.8", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "tsx": "^4.6.2", "typescript": "^5.3.3"}}