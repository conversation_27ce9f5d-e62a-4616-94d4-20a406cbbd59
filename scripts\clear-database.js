#!/usr/bin/env node

// <PERSON>ript to clear all data from SQLite database
// Usage: node scripts/clear-database.js

import fetch from 'node-fetch';

const SERVER_URL = 'http://localhost:3002';

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${SERVER_URL}/api/health`);
    const result = await response.json();
    return result.success;
  } catch (error) {
    return false;
  }
}

// Clear database
async function clearDatabase() {
  try {
    console.log('🗑️  Clearing database...');
    
    const response = await fetch(`${SERVER_URL}/api/migration/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ confirm: 'CLEAR_ALL_DATA' })
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Database cleared successfully!');
      console.log('📊 Cleared tables:', result.cleared || 'All tables');
    } else {
      console.error('❌ Failed to clear database:', result.message);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Clear operation failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the SQLite server is running:');
      console.error('   cd server && npm run dev');
    }
    process.exit(1);
  }
}

// Get database statistics
async function getDatabaseStats() {
  try {
    const response = await fetch(`${SERVER_URL}/api/health`);
    const result = await response.json();
    
    if (result.success && result.stats) {
      console.log('📊 Database statistics:');
      Object.entries(result.stats).forEach(([table, stats]) => {
        console.log(`   - ${table}: ${stats.count} records`);
      });
    }
  } catch (error) {
    console.warn('⚠️  Could not fetch database statistics');
  }
}

// Main execution
async function main() {
  console.log('🗑️  Database Cleaner for SQLite');
  console.log('===============================');

  // Check if server is running
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.error('❌ SQLite server is not running!');
    console.error('💡 Please start the server first:');
    console.error('   cd server && npm run dev');
    process.exit(1);
  }

  console.log('✅ SQLite server is running');

  // Show current database stats
  console.log('\n📊 Before clearing:');
  await getDatabaseStats();

  // Ask for confirmation
  console.log('\n⚠️  This will permanently delete ALL data in the database.');
  console.log('Are you sure you want to continue? This action cannot be undone.');
  
  // For automated scripts, you can skip this confirmation
  const shouldContinue = process.argv.includes('--force') || process.argv.includes('-f');
  
  if (!shouldContinue) {
    console.log('💡 Use --force or -f flag to skip confirmation');
    console.log('Aborting operation for safety.');
    process.exit(0);
  }

  // Clear database
  await clearDatabase();

  // Show final database stats
  console.log('\n📊 After clearing:');
  await getDatabaseStats();

  console.log('\n🎉 Database cleared successfully!');
  console.log('💡 You can now load fresh data using the mock data loader');
}

main().catch(console.error);
