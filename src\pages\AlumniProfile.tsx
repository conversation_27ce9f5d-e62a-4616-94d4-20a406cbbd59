import React, { useState, useEffect } from 'react';
import { Save, User, Mail, Phone, MapPin, GraduationCap, Calendar, Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/Layout';
import { dataManager } from '@/utils/dataManager';

interface AlumniData {
  id: string;
  nim: string;
  namaLengkap: string;
  programStudi?: string;
  fakultas?: string;
  tahunMasuk?: number;
  tahunLulus?: number;
  ipk?: number;
  email?: string;
  noTelepon?: string;
  alamat?: string;
  statusVerifikasi: 'pending' | 'verified' | 'rejected';
}

const AlumniProfile = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [alumniData, setAlumniData] = useState<AlumniData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    loadAlumniData();
  }, [user]);

  const loadAlumniData = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      // Find alumni data by user email or ID
      const allAlumni = await dataManager.getAlumni();
      const currentAlumni = allAlumni.find(alumni => 
        alumni.email === user.email || alumni.userId === user.id
      );
      
      if (currentAlumni) {
        setAlumniData(currentAlumni);
      } else {
        toast({
          title: "Data tidak ditemukan",
          description: "Data alumni Anda tidak ditemukan dalam sistem.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading alumni data:', error);
      toast({
        title: "Error",
        description: "Gagal memuat data alumni.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof AlumniData, value: string | number) => {
    if (!alumniData) return;
    
    setAlumniData(prev => ({
      ...prev!,
      [field]: value
    }));
  };

  const handleSave = async () => {
    if (!alumniData) return;
    
    try {
      setIsSaving(true);
      await dataManager.updateAlumni(alumniData.id, alumniData);
      
      toast({
        title: "Berhasil",
        description: "Data alumni berhasil diperbarui.",
      });
    } catch (error) {
      console.error('Error saving alumni data:', error);
      toast({
        title: "Error",
        description: "Gagal menyimpan data alumni.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      pending: { label: 'Menunggu Verifikasi', variant: 'secondary' as const },
      verified: { label: 'Terverifikasi', variant: 'default' as const },
      rejected: { label: 'Ditolak', variant: 'destructive' as const }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Memuat data alumni...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!alumniData) {
    return (
      <Layout>
        <div className="text-center py-12">
          <User className="h-16 w-16 mx-auto text-gray-300 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Data Alumni Tidak Ditemukan</h2>
          <p className="text-gray-600">Silakan hubungi administrator untuk menambahkan data Anda.</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Data Alumni Saya</h1>
            <p className="text-gray-600">Kelola dan perbarui informasi data alumni Anda</p>
          </div>
          <div className="flex items-center space-x-3">
            {getStatusBadge(alumniData.statusVerifikasi)}
            <Button 
              onClick={handleSave} 
              disabled={isSaving}
              className="university-gradient"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Menyimpan...' : 'Simpan Perubahan'}
            </Button>
          </div>
        </div>

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Informasi Pribadi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="nim">NIM</Label>
                <Input
                  id="nim"
                  value={alumniData.nim}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <Label htmlFor="namaLengkap">Nama Lengkap</Label>
                <Input
                  id="namaLengkap"
                  value={alumniData.namaLengkap}
                  onChange={(e) => handleInputChange('namaLengkap', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={alumniData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="noTelepon">No. Telepon</Label>
                <Input
                  id="noTelepon"
                  value={alumniData.noTelepon || ''}
                  onChange={(e) => handleInputChange('noTelepon', e.target.value)}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="alamat">Alamat</Label>
              <Input
                id="alamat"
                value={alumniData.alamat || ''}
                onChange={(e) => handleInputChange('alamat', e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Academic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <GraduationCap className="h-5 w-5 mr-2" />
              Informasi Akademik
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="fakultas">Fakultas</Label>
                <Input
                  id="fakultas"
                  value={alumniData.fakultas || ''}
                  onChange={(e) => handleInputChange('fakultas', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="programStudi">Program Studi</Label>
                <Input
                  id="programStudi"
                  value={alumniData.programStudi || ''}
                  onChange={(e) => handleInputChange('programStudi', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="tahunMasuk">Tahun Masuk</Label>
                <Input
                  id="tahunMasuk"
                  type="number"
                  value={alumniData.tahunMasuk || ''}
                  onChange={(e) => handleInputChange('tahunMasuk', parseInt(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="tahunLulus">Tahun Lulus</Label>
                <Input
                  id="tahunLulus"
                  type="number"
                  value={alumniData.tahunLulus || ''}
                  onChange={(e) => handleInputChange('tahunLulus', parseInt(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="ipk">IPK</Label>
                <Input
                  id="ipk"
                  type="number"
                  step="0.01"
                  min="0"
                  max="4"
                  value={alumniData.ipk || ''}
                  onChange={(e) => handleInputChange('ipk', parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default AlumniProfile;
