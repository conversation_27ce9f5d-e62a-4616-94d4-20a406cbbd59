// Database types matching the frontend interfaces

export interface AlumniData {
  id: string;
  user_id?: string;
  nim: string;
  nama_lengkap: string;
  program_studi?: string;
  fakultas?: string;
  tahun_masuk?: number;
  tahun_lulus?: number;
  ipk?: number;
  email?: string;
  no_telepon?: string;
  alamat?: string;
  status_verifikasi: 'pending' | 'verified' | 'rejected';
  created_at: string;
  updated_at: string;
}

export interface EmploymentData {
  id: string;
  alumni_id: string;
  nama_perusahaan: string;
  posisi_jabatan: string;
  jenis_usaha?: string;
  gaji_pertama?: number;
  gaji_saat_ini?: number;
  tanggal_mulai_kerja?: string;
  status_pekerjaan: 'bekerja' | 'tidak_bekerja' | 'wirausaha';
  relevansi_pekerjaan?: 'sangat_relevan' | 'relevan' | 'kurang_relevan' | 'tidak_relevan';
  created_at: string;
  updated_at: string;
}

export interface SurveyData {
  id: string;
  judul: string;
  deskripsi?: string;
  tanggal_mulai?: string;
  tanggal_selesai?: string;
  status: 'active' | 'inactive';
  target_alumni?: string; // JSON string
  questions?: string; // JSON string
  created_at: string;
  updated_at: string;
}

export interface SurveyQuestion {
  id: string;
  type: 'text' | 'multiple_choice' | 'rating' | 'checkbox';
  question: string;
  options?: string[];
  required: boolean;
}

export interface SurveyResponse {
  id: string;
  survey_id: string;
  alumni_id: string;
  responses: string; // JSON string
  submitted_at: string;
}

export interface UserData {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'staff' | 'alumni';
  nama_lengkap: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface ReportData {
  id: string;
  title: string;
  type: 'employment' | 'survey' | 'alumni' | 'custom';
  generated_by: string;
  generated_at: string;
  data?: string; // JSON string
  filters?: string; // JSON string
}

export interface SettingsData {
  id: string;
  key: string;
  value: string; // JSON string
  description?: string;
  updated_at: string;
}

// Request/Response types for API
export interface CreateAlumniRequest {
  user_id?: string;
  nim: string;
  nama_lengkap: string;
  program_studi?: string;
  fakultas?: string;
  tahun_masuk?: number;
  tahun_lulus?: number;
  ipk?: number;
  email?: string;
  no_telepon?: string;
  alamat?: string;
  status_verifikasi?: 'pending' | 'verified' | 'rejected';
}

export interface UpdateAlumniRequest {
  user_id?: string;
  nim?: string;
  nama_lengkap?: string;
  program_studi?: string;
  fakultas?: string;
  tahun_masuk?: number;
  tahun_lulus?: number;
  ipk?: number;
  email?: string;
  no_telepon?: string;
  alamat?: string;
  status_verifikasi?: 'pending' | 'verified' | 'rejected';
}

export interface CreateEmploymentRequest {
  alumni_id: string;
  nama_perusahaan: string;
  posisi_jabatan: string;
  jenis_usaha?: string;
  gaji_pertama?: number;
  gaji_saat_ini?: number;
  tanggal_mulai_kerja?: string;
  status_pekerjaan: 'bekerja' | 'tidak_bekerja' | 'wirausaha';
  relevansi_pekerjaan?: 'sangat_relevan' | 'relevan' | 'kurang_relevan' | 'tidak_relevan';
}

export interface CreateSurveyRequest {
  judul: string;
  deskripsi?: string;
  tanggal_mulai?: string;
  tanggal_selesai?: string;
  status?: 'active' | 'inactive';
  target_alumni?: string[];
  questions?: SurveyQuestion[];
}

export interface CreateUserRequest {
  username: string;
  email: string;
  role: 'admin' | 'staff' | 'alumni';
  nama_lengkap: string;
  is_active?: boolean; // Optional, defaults to true
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  status?: string;
  program_studi?: string;
  fakultas?: string;
  tahun_lulus?: number;
  search?: string;
}
