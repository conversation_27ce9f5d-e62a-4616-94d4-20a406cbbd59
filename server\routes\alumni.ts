import express from 'express';
import AlumniModel from '../models/AlumniModel';
import { CreateAlumniRequest, UpdateAlumniRequest, FilterParams, PaginationParams } from '../models/types';

const router = express.Router();
const alumniModel = new AlumniModel();

// GET /api/alumni - Get all alumni with optional filtering and pagination
router.get('/', (req, res) => {
  try {
    const filters: FilterParams = {
      status: req.query.status as string,
      program_studi: req.query.program_studi as string,
      fakultas: req.query.fakultas as string,
      tahun_lulus: req.query.tahun_lulus ? parseInt(req.query.tahun_lulus as string) : undefined,
      search: req.query.search as string
    };

    const pagination: PaginationParams = {
      page: req.query.page ? parseInt(req.query.page as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      sortBy: req.query.sortBy as string,
      sortOrder: req.query.sortOrder as 'asc' | 'desc'
    };

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof FilterParams] === undefined) {
        delete filters[key as keyof FilterParams];
      }
    });

    const alumni = alumniModel.findAll(
      Object.keys(filters).length > 0 ? filters : undefined,
      pagination.page || pagination.limit ? pagination : undefined
    );

    const total = alumniModel.getCount(Object.keys(filters).length > 0 ? filters : undefined);

    res.json({
      success: true,
      data: alumni,
      pagination: {
        total,
        page: pagination.page || 1,
        limit: pagination.limit || total,
        totalPages: pagination.limit ? Math.ceil(total / pagination.limit) : 1
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/alumni/statistics - Get alumni statistics
router.get('/statistics', (req, res) => {
  try {
    const statistics = alumniModel.getStatistics();
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/alumni/:id - Get alumni by ID
router.get('/:id', (req, res) => {
  try {
    const alumni = alumniModel.findById(req.params.id);
    if (!alumni) {
      return res.status(404).json({
        success: false,
        message: 'Alumni not found'
      });
    }

    res.json({
      success: true,
      data: alumni
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/alumni - Create new alumni
router.post('/', (req, res) => {
  try {
    const data: CreateAlumniRequest = req.body;

    // Validate required fields
    if (!data.nim || !data.nama_lengkap) {
      return res.status(400).json({
        success: false,
        message: 'NIM and nama_lengkap are required'
      });
    }

    // Check if NIM already exists
    const existing = alumniModel.findByNim(data.nim);
    if (existing) {
      return res.status(400).json({
        success: false,
        message: 'NIM already exists'
      });
    }

    const alumni = alumniModel.create(data);
    res.status(201).json({
      success: true,
      data: alumni,
      message: 'Alumni created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/alumni/:id - Update alumni
router.put('/:id', (req, res) => {
  try {
    const data: UpdateAlumniRequest = req.body;
    const alumni = alumniModel.update(req.params.id, data);
    
    if (!alumni) {
      return res.status(404).json({
        success: false,
        message: 'Alumni not found'
      });
    }

    res.json({
      success: true,
      data: alumni,
      message: 'Alumni updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/alumni/:id - Delete alumni
router.delete('/:id', (req, res) => {
  try {
    const success = alumniModel.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Alumni not found'
      });
    }

    res.json({
      success: true,
      message: 'Alumni deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/alumni/bulk - Bulk insert alumni
router.post('/bulk', (req, res) => {
  try {
    const alumniList: CreateAlumniRequest[] = req.body;
    
    if (!Array.isArray(alumniList) || alumniList.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid data format. Expected array of alumni objects.'
      });
    }

    const count = alumniModel.bulkInsert(alumniList);
    res.json({
      success: true,
      message: `Successfully inserted ${count} alumni records`,
      data: { count }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to bulk insert alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/alumni/nim/:nim - Find alumni by NIM
router.get('/nim/:nim', (req, res) => {
  try {
    const alumni = alumniModel.findByNim(req.params.nim);
    if (!alumni) {
      return res.status(404).json({
        success: false,
        message: 'Alumni not found'
      });
    }

    res.json({
      success: true,
      data: alumni
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
