// Test script to verify Survey CRUD operations
const API_BASE_URL = 'http://localhost:3002/api';

async function testSurveyCRUD() {
  console.log('🧪 Testing Survey CRUD Operations...\n');

  // Test data
  const testSurvey = {
    judul: 'Test Survey CRUD',
    deskripsi: 'Survey untuk testing CRUD operations',
    tanggal_mulai: '2024-01-01',
    tanggal_selesai: '2024-12-31',
    status: 'active',
    target_alumni: ['ALM001', 'ALM002'],
    questions: [
      {
        id: 'Q1',
        type: 'text',
        question: 'Apa pendapat Anda?',
        required: true,
        options: []
      }
    ]
  };

  let createdSurveyId = null;

  try {
    // 1. CREATE - Test creating a new survey
    console.log('1️⃣ Testing CREATE operation...');
    const createResponse = await fetch(`${API_BASE_URL}/surveys`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSurvey),
    });

    if (!createResponse.ok) {
      throw new Error(`Create failed: ${createResponse.status}`);
    }

    const createResult = await createResponse.json();
    if (createResult.success && createResult.data) {
      createdSurveyId = createResult.data.id;
      console.log('✅ CREATE successful:', createdSurveyId);
    } else {
      throw new Error('Create response invalid');
    }

    // 2. READ - Test reading all surveys
    console.log('\n2️⃣ Testing READ (all) operation...');
    const readAllResponse = await fetch(`${API_BASE_URL}/surveys`);
    const readAllResult = await readAllResponse.json();
    
    if (readAllResult.success && Array.isArray(readAllResult.data)) {
      console.log('✅ READ (all) successful:', readAllResult.data.length, 'surveys found');
    } else {
      throw new Error('Read all failed');
    }

    // 3. READ - Test reading specific survey
    console.log('\n3️⃣ Testing READ (by ID) operation...');
    const readByIdResponse = await fetch(`${API_BASE_URL}/surveys/${createdSurveyId}`);
    const readByIdResult = await readByIdResponse.json();
    
    if (readByIdResult.success && readByIdResult.data) {
      console.log('✅ READ (by ID) successful:', readByIdResult.data.judul);
    } else {
      throw new Error('Read by ID failed');
    }

    // 4. UPDATE - Test updating the survey
    console.log('\n4️⃣ Testing UPDATE operation...');
    const updateData = {
      judul: 'Updated Test Survey',
      deskripsi: 'Updated description for testing',
      status: 'inactive'
    };

    const updateResponse = await fetch(`${API_BASE_URL}/surveys/${createdSurveyId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData),
    });

    const updateResult = await updateResponse.json();
    if (updateResult.success && updateResult.data) {
      console.log('✅ UPDATE successful:', updateResult.data.judul);
    } else {
      throw new Error('Update failed');
    }

    // 5. DELETE - Test deleting the survey
    console.log('\n5️⃣ Testing DELETE operation...');
    const deleteResponse = await fetch(`${API_BASE_URL}/surveys/${createdSurveyId}`, {
      method: 'DELETE',
    });

    const deleteResult = await deleteResponse.json();
    if (deleteResult.success) {
      console.log('✅ DELETE successful');
    } else {
      throw new Error('Delete failed');
    }

    // 6. Verify deletion
    console.log('\n6️⃣ Verifying deletion...');
    const verifyResponse = await fetch(`${API_BASE_URL}/surveys/${createdSurveyId}`);
    if (verifyResponse.status === 404) {
      console.log('✅ Deletion verified - survey not found (expected)');
    } else {
      console.log('⚠️ Warning: Survey still exists after deletion');
    }

    console.log('\n🎉 All CRUD operations completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    // Cleanup: try to delete the created survey if it exists
    if (createdSurveyId) {
      try {
        await fetch(`${API_BASE_URL}/surveys/${createdSurveyId}`, {
          method: 'DELETE',
        });
        console.log('🧹 Cleanup: Test survey deleted');
      } catch (cleanupError) {
        console.log('⚠️ Cleanup failed:', cleanupError.message);
      }
    }
  }
}

// Run the test
testSurveyCRUD();
