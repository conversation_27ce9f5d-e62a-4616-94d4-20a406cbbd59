
// Utility untuk mengelola data JSON di localStorage
export interface AlumniData {
  id: string;
  userId: string;
  nim: string;
  namaLengkap: string;
  programStudi: string;
  fakultas: string;
  tahunMasuk: number;
  tahunLulus: number;
  ipk: number;
  email: string;
  noTelepon: string;
  alamat: string;
  statusVerifikasi: 'pending' | 'verified' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface EmploymentData {
  id: string;
  alumniId: string;
  namaPerusahaan: string;
  posisiJabatan: string;
  jenisUsaha: string;
  gajiPertama: number;
  gajiSaatIni: number;
  tanggalMulaiKerja: string;
  statusPekerjaan: 'bekerja' | 'tidak_bekerja' | 'wirausaha';
  relevansiPekerjaan: 'sangat_relevan' | 'relevan' | 'kurang_relevan' | 'tidak_relevan';
}

export interface SurveyData {
  id: string;
  judul: string;
  deskripsi: string;
  tanggalMulai: string;
  tanggalSelesai: string;
  status: 'active' | 'inactive';
  targetAlumni: string[];
  questions: SurveyQuestion[];
  responses: SurveyResponse[];
}

export interface SurveyQuestion {
  id: string;
  type: 'text' | 'multiple_choice' | 'rating' | 'checkbox';
  question: string;
  options?: string[];
  required: boolean;
}

export interface SurveyResponse {
  id: string;
  surveyId: string;
  alumniId: string;
  responses: { [questionId: string]: any };
  submittedAt: string;
}

export interface UserData {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'staff' | 'alumni';
  namaLengkap: string;
  createdAt: string;
  lastLogin: string;
}

export interface ReportData {
  id: string;
  title: string;
  type: 'employment' | 'survey' | 'alumni' | 'custom';
  generatedBy: string;
  generatedAt: string;
  data: any;
  filters: any;
}

export interface SettingsData {
  id: string;
  key: string;
  value: any;
  description: string;
  updatedAt: string;
}

class DataManager {
  private getStorageKey(dataType: string): string {
    return `tracer_${dataType}`;
  }

  // Utility methods for ID generation
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private generateAlumniId(): string {
    const alumni = this.getAlumniData();
    const currentYear = new Date().getFullYear();
    const count = alumni.filter(a => a.id.startsWith(`ALM${currentYear}`)).length + 1;
    return `ALM${currentYear}${count.toString().padStart(4, '0')}`;
  }

  private generateSurveyId(): string {
    const surveys = this.getSurveyData();
    const count = surveys.length + 1;
    return `SRV${count.toString().padStart(4, '0')}`;
  }

  // Generic methods for CRUD operations
  private getData<T>(dataType: string): T[] {
    const data = localStorage.getItem(this.getStorageKey(dataType));
    return data ? JSON.parse(data) : [];
  }

  private saveData<T>(dataType: string, data: T[]): void {
    localStorage.setItem(this.getStorageKey(dataType), JSON.stringify(data));
  }

  // Generic CRUD operations
  private createRecord<T extends { id: string; createdAt: string; updatedAt: string }>(
    dataType: string,
    record: Omit<T, 'id' | 'createdAt' | 'updatedAt'>,
    idGenerator?: () => string
  ): T {
    const data = this.getData<T>(dataType);
    const newRecord = {
      ...record,
      id: idGenerator ? idGenerator() : this.generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as T;

    data.push(newRecord);
    this.saveData(dataType, data);
    return newRecord;
  }

  private updateRecord<T extends { id: string; updatedAt: string }>(
    dataType: string,
    id: string,
    updates: Partial<Omit<T, 'id' | 'createdAt'>>
  ): T | null {
    const data = this.getData<T>(dataType);
    const index = data.findIndex(item => item.id === id);

    if (index === -1) return null;

    data[index] = {
      ...data[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    this.saveData(dataType, data);
    return data[index];
  }

  private deleteRecord<T extends { id: string }>(dataType: string, id: string): boolean {
    const data = this.getData<T>(dataType);
    const index = data.findIndex(item => item.id === id);

    if (index === -1) return false;

    data.splice(index, 1);
    this.saveData(dataType, data);
    return true;
  }

  // Alumni data management
  getAlumniData(): AlumniData[] {
    return this.getData<AlumniData>('alumni');
  }

  saveAlumniData(alumni: AlumniData[]): void {
    this.saveData('alumni', alumni);
  }

  getAlumniById(id: string): AlumniData | null {
    const alumni = this.getAlumniData();
    return alumni.find(a => a.id === id) || null;
  }

  addAlumni(alumni: Omit<AlumniData, 'id' | 'createdAt' | 'updatedAt'>): AlumniData {
    return this.createRecord<AlumniData>('alumni', alumni, () => this.generateAlumniId());
  }

  updateAlumni(id: string, updates: Partial<Omit<AlumniData, 'id' | 'createdAt'>>): AlumniData | null {
    return this.updateRecord<AlumniData>('alumni', id, updates);
  }

  deleteAlumni(id: string): boolean {
    return this.deleteRecord<AlumniData>('alumni', id);
  }

  // Employment data management
  getEmploymentData(): EmploymentData[] {
    return this.getData<EmploymentData>('employment');
  }

  getEmploymentById(id: string): EmploymentData | null {
    const employment = this.getEmploymentData();
    return employment.find(e => e.id === id) || null;
  }

  getEmploymentByAlumniId(alumniId: string): EmploymentData[] {
    const employment = this.getEmploymentData();
    return employment.filter(e => e.alumniId === alumniId);
  }

  addEmployment(employment: Omit<EmploymentData, 'id' | 'createdAt' | 'updatedAt'>): EmploymentData {
    return this.createRecord<EmploymentData>('employment', employment);
  }

  updateEmployment(id: string, updates: Partial<Omit<EmploymentData, 'id' | 'createdAt'>>): EmploymentData | null {
    return this.updateRecord<EmploymentData>('employment', id, updates);
  }

  deleteEmployment(id: string): boolean {
    return this.deleteRecord<EmploymentData>('employment', id);
  }

  saveEmploymentData(employment: EmploymentData[]): void {
    this.saveData('employment', employment);
  }

  // Survey data management
  getSurveyData(): SurveyData[] {
    return this.getData<SurveyData>('surveys');
  }

  getSurveyById(id: string): SurveyData | null {
    const surveys = this.getSurveyData();
    return surveys.find(s => s.id === id) || null;
  }

  addSurvey(survey: Omit<SurveyData, 'id' | 'createdAt' | 'updatedAt'>): SurveyData {
    const surveyWithDefaults = {
      ...survey,
      responses: survey.responses || []
    };
    return this.createRecord<SurveyData>('surveys', surveyWithDefaults, () => this.generateSurveyId());
  }

  updateSurvey(id: string, updates: Partial<Omit<SurveyData, 'id' | 'createdAt'>>): SurveyData | null {
    return this.updateRecord<SurveyData>('surveys', id, updates);
  }

  deleteSurvey(id: string): boolean {
    return this.deleteRecord<SurveyData>('surveys', id);
  }

  saveSurveyData(surveys: SurveyData[]): void {
    this.saveData('surveys', surveys);
  }

  // User data management
  getUserData(): UserData[] {
    return this.getData<UserData>('users');
  }

  getUserById(id: string): UserData | null {
    const users = this.getUserData();
    return users.find(u => u.id === id) || null;
  }

  getUserByEmail(email: string): UserData | null {
    const users = this.getUserData();
    return users.find(u => u.email === email) || null;
  }

  addUser(user: Omit<UserData, 'id' | 'createdAt' | 'updatedAt'>): UserData {
    return this.createRecord<UserData>('users', user);
  }

  updateUser(id: string, updates: Partial<Omit<UserData, 'id' | 'createdAt'>>): UserData | null {
    return this.updateRecord<UserData>('users', id, updates);
  }

  deleteUser(id: string): boolean {
    return this.deleteRecord<UserData>('users', id);
  }

  saveUserData(users: UserData[]): void {
    this.saveData('users', users);
  }

  // Survey Response data management
  getSurveyResponses(): SurveyResponse[] {
    return this.getData<SurveyResponse>('survey_responses');
  }

  getSurveyResponseById(id: string): SurveyResponse | null {
    const responses = this.getSurveyResponses();
    return responses.find(r => r.id === id) || null;
  }

  getSurveyResponsesBySurveyId(surveyId: string): SurveyResponse[] {
    const responses = this.getSurveyResponses();
    return responses.filter(r => r.surveyId === surveyId);
  }

  addSurveyResponse(response: Omit<SurveyResponse, 'id'>): SurveyResponse {
    const responseWithId = {
      ...response,
      id: `RESP${Date.now()}`,
    };

    const responses = this.getSurveyResponses();
    responses.push(responseWithId);
    this.saveData('survey_responses', responses);

    return responseWithId;
  }

  updateSurveyResponse(id: string, updates: Partial<Omit<SurveyResponse, 'id'>>): SurveyResponse | null {
    const responses = this.getSurveyResponses();
    const index = responses.findIndex(r => r.id === id);

    if (index === -1) return null;

    responses[index] = { ...responses[index], ...updates };
    this.saveData('survey_responses', responses);

    return responses[index];
  }

  deleteSurveyResponse(id: string): boolean {
    const responses = this.getSurveyResponses();
    const index = responses.findIndex(r => r.id === id);

    if (index === -1) return false;

    responses.splice(index, 1);
    this.saveData('survey_responses', responses);

    return true;
  }

  // Report data management
  getReportData(): ReportData[] {
    return this.getData<ReportData>('reports');
  }

  saveReportData(reports: ReportData[]): void {
    this.saveData('reports', reports);
  }

  addReport(report: Omit<ReportData, 'id' | 'generatedAt'>): ReportData {
    const allReports = this.getReportData();
    const newReport: ReportData = {
      ...report,
      id: Date.now().toString(),
      generatedAt: new Date().toISOString()
    };

    allReports.push(newReport);
    this.saveReportData(allReports);
    return newReport;
  }

  // Settings data management
  getSettingsData(): SettingsData[] {
    return this.getData<SettingsData>('settings');
  }

  saveSettingsData(settings: SettingsData[]): void {
    this.saveData('settings', settings);
  }

  getSetting(key: string): SettingsData | null {
    const settings = this.getSettingsData();
    return settings.find(s => s.key === key) || null;
  }

  updateSetting(key: string, value: any): SettingsData {
    const settings = this.getSettingsData();
    const existingIndex = settings.findIndex(s => s.key === key);

    const setting: SettingsData = {
      id: existingIndex >= 0 ? settings[existingIndex].id : Date.now().toString(),
      key,
      value,
      description: existingIndex >= 0 ? settings[existingIndex].description : '',
      updatedAt: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      settings[existingIndex] = setting;
    } else {
      settings.push(setting);
    }

    this.saveSettingsData(settings);
    return setting;
  }

  // Initialize with sample data (disabled - using real data only)
  initializeSampleData(): void {
    // Disabled - only use real data from JSON database
    return;

    // Sample data creation disabled - using real data only






  }

  // Clear all data (for development/testing purposes)
  clearAllData(): void {
    const keys = ['alumni', 'employment', 'surveys', 'users', 'settings', 'survey_responses'];
    keys.forEach(key => {
      localStorage.removeItem(key);
    });
  }

  // Check if database is empty
  isDatabaseEmpty(): boolean {
    return this.getAlumniData().length === 0 &&
           this.getEmploymentData().length === 0 &&
           this.getSurveyData().length === 0 &&
           this.getUserData().length === 0;
  }
}

export const dataManager = new DataManager();

// Sample data initialization disabled - using real data only
