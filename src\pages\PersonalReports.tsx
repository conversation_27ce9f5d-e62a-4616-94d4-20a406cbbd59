import React, { useState, useEffect } from 'react';
import { BarChart3, Download, User, Building, ClipboardList, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/Layout';
import { dataManager } from '@/utils/dataManager';
import { sqliteDataManager } from '@/utils/sqliteDataManager';

interface PersonalStats {
  profileCompletion: number;
  employmentStatus: string;
  surveysCompleted: number;
  totalSurveys: number;
  lastUpdated: string;
}

const PersonalReports = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<PersonalStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadPersonalStats();
  }, [user]);

  const loadPersonalStats = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Check if server is available, use SQLite if available, fallback to localStorage
      const isServerAvailable = await sqliteDataManager.isServerAvailable();
      let allAlumni, allEmployment, allSurveys;

      if (isServerAvailable) {
        allAlumni = await sqliteDataManager.getAlumniData();
        allEmployment = await sqliteDataManager.getEmploymentData();
        allSurveys = await sqliteDataManager.getSurveyData();
      } else {
        allAlumni = dataManager.getAlumniData();
        allEmployment = dataManager.getEmploymentData();
        allSurveys = dataManager.getSurveyData();
      }

      // Get alumni data
      const currentAlumni = allAlumni.find(alumni =>
        alumni.email === user.email || alumni.userId === user.id
      );

      // Get employment data
      const userEmployment = allEmployment.filter(emp => emp.alumniId === currentAlumni?.id);

      // Get surveys data
      const activeSurveys = allSurveys.filter(survey => survey.status === 'active');
      
      // Calculate profile completion
      let completedFields = 0;
      const totalFields = 10; // Total fields to check
      
      if (currentAlumni) {
        if (currentAlumni.namaLengkap) completedFields++;
        if (currentAlumni.email) completedFields++;
        if (currentAlumni.noTelepon) completedFields++;
        if (currentAlumni.alamat) completedFields++;
        if (currentAlumni.fakultas) completedFields++;
        if (currentAlumni.programStudi) completedFields++;
        if (currentAlumni.tahunMasuk) completedFields++;
        if (currentAlumni.tahunLulus) completedFields++;
        if (currentAlumni.ipk) completedFields++;
        if (currentAlumni.statusVerifikasi === 'verified') completedFields++;
      }
      
      const profileCompletion = Math.round((completedFields / totalFields) * 100);
      
      // Determine employment status
      let employmentStatus = 'Belum Diisi';
      if (userEmployment.length > 0) {
        const latestEmployment = userEmployment[userEmployment.length - 1];
        employmentStatus = latestEmployment.statusPekerjaan || 'Tidak Diketahui';
      }
      
      setStats({
        profileCompletion,
        employmentStatus,
        surveysCompleted: 0, // This would need to be calculated from survey responses
        totalSurveys: activeSurveys.length,
        lastUpdated: currentAlumni?.updatedAt || new Date().toISOString()
      });
      
    } catch (error) {
      console.error('Error loading personal stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getEmploymentStatusBadge = (status: string) => {
    const statusMap = {
      'bekerja': { label: 'Bekerja', variant: 'default' as const },
      'tidak_bekerja': { label: 'Tidak Bekerja', variant: 'secondary' as const },
      'wirausaha': { label: 'Wirausaha', variant: 'outline' as const },
      'Belum Diisi': { label: 'Belum Diisi', variant: 'destructive' as const }
    };
    
    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap['Belum Diisi'];
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleExportData = () => {
    // This would export the user's personal data
    console.log('Exporting personal data...');
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Memuat laporan personal...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Laporan & Analytics Personal</h1>
            <p className="text-gray-600">Ringkasan data dan statistik personal Anda</p>
          </div>
          <Button onClick={handleExportData} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Kelengkapan Profil</p>
                  <p className={`text-2xl font-bold ${getCompletionColor(stats?.profileCompletion || 0)}`}>
                    {stats?.profileCompletion || 0}%
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Status Pekerjaan</p>
                  <div className="mt-1">
                    {getEmploymentStatusBadge(stats?.employmentStatus || 'Belum Diisi')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <ClipboardList className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Survey Selesai</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stats?.surveysCompleted || 0}/{stats?.totalSurveys || 0}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Terakhir Update</p>
                  <p className="text-sm text-gray-900">
                    {stats?.lastUpdated ? new Date(stats.lastUpdated).toLocaleDateString('id-ID') : '-'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Profile Completion Details */}
        <Card>
          <CardHeader>
            <CardTitle>Detail Kelengkapan Profil</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Kelengkapan Data Pribadi</span>
                <div className="flex items-center space-x-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${stats?.profileCompletion || 0}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{stats?.profileCompletion || 0}%</span>
                </div>
              </div>
              
              <div className="text-sm text-gray-600">
                <p>Untuk meningkatkan kelengkapan profil Anda:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Lengkapi semua field di halaman Data Alumni</li>
                  <li>Pastikan data pekerjaan sudah diisi</li>
                  <li>Verifikasi email dan nomor telepon</li>
                  <li>Tunggu verifikasi dari administrator</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Aksi Cepat</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2"
                onClick={() => window.location.href = '/alumni/profile'}
              >
                <User className="h-6 w-6" />
                <span>Update Profil</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2"
                onClick={() => window.location.href = '/employment'}
              >
                <Building className="h-6 w-6" />
                <span>Update Pekerjaan</span>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-auto p-4 flex flex-col items-center space-y-2"
                onClick={() => window.location.href = '/surveys'}
              >
                <ClipboardList className="h-6 w-6" />
                <span>Isi Survey</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default PersonalReports;
