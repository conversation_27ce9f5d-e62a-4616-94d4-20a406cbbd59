import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Handle __dirname in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DatabaseConnection {
  private db: Database.Database;
  private static instance: DatabaseConnection;

  private constructor() {
    // Create database directory if it doesn't exist
    const dbDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Initialize database
    const dbPath = path.join(dbDir, 'tracer_alumni.db');
    this.db = new Database(dbPath);
    
    // Enable foreign keys
    this.db.pragma('foreign_keys = ON');
    
    // Initialize schema
    this.initializeSchema();
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  private initializeSchema(): void {
    try {
      // First, check if we need to run migrations
      this.runMigrations();

      const schemaPath = path.join(__dirname, 'schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');

      // Split schema by semicolons and execute each statement
      const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);

      statements.forEach(statement => {
        try {
          this.db.exec(statement + ';');
        } catch (error) {
          // Ignore errors for statements that might already exist (like CREATE TABLE IF NOT EXISTS)
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (!errorMessage.includes('already exists') && !errorMessage.includes('duplicate column name')) {
            throw error;
          }
        }
      });

      console.log('Database schema initialized successfully');
    } catch (error) {
      console.error('Error initializing database schema:', error);
      throw error;
    }
  }

  private runMigrations(): void {
    try {
      // Check if users table exists and if it has is_active column
      const tableInfo = this.db.prepare("PRAGMA table_info(users)").all();
      const hasIsActiveColumn = tableInfo.some((col: any) => col.name === 'is_active');

      if (tableInfo.length > 0 && !hasIsActiveColumn) {
        console.log('Adding is_active column to users table...');
        this.db.exec('ALTER TABLE users ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT 1');
        this.db.exec('UPDATE users SET is_active = 1 WHERE is_active IS NULL');
        console.log('Migration completed: is_active column added');
      }
    } catch (error) {
      // If users table doesn't exist, that's fine - it will be created by the schema
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (!errorMessage.includes('no such table')) {
        console.error('Migration error:', error);
      }
    }
  }

  public close(): void {
    if (this.db) {
      this.db.close();
    }
  }

  // Utility methods for common operations
  public beginTransaction(): Database.Transaction {
    return this.db.transaction(() => {});
  }

  public backup(backupPath: string): void {
    try {
      this.db.backup(backupPath);
      console.log(`Database backed up to: ${backupPath}`);
    } catch (error) {
      console.error('Error backing up database:', error);
      throw error;
    }
  }

  public getStats(): any {
    const stats = {
      alumni: this.db.prepare('SELECT COUNT(*) as count FROM alumni').get(),
      employment: this.db.prepare('SELECT COUNT(*) as count FROM employment').get(),
      surveys: this.db.prepare('SELECT COUNT(*) as count FROM surveys').get(),
      users: this.db.prepare('SELECT COUNT(*) as count FROM users').get(),
      survey_responses: this.db.prepare('SELECT COUNT(*) as count FROM survey_responses').get(),
      reports: this.db.prepare('SELECT COUNT(*) as count FROM reports').get(),
      settings: this.db.prepare('SELECT COUNT(*) as count FROM settings').get()
    };
    
    return stats;
  }

  public vacuum(): void {
    this.db.exec('VACUUM');
    console.log('Database vacuumed successfully');
  }
}

export default DatabaseConnection;
