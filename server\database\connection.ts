import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Handle __dirname in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DatabaseConnection {
  private db: Database.Database;
  private static instance: DatabaseConnection;

  private constructor() {
    // Create database directory if it doesn't exist
    const dbDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Initialize database
    const dbPath = path.join(dbDir, 'tracer_alumni.db');
    this.db = new Database(dbPath);
    
    // Enable foreign keys
    this.db.pragma('foreign_keys = ON');
    
    // Initialize schema
    this.initializeSchema();
  }

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  private initializeSchema(): void {
    try {
      const schemaPath = path.join(__dirname, 'schema.sql');
      const schema = fs.readFileSync(schemaPath, 'utf8');
      
      // Split schema by semicolons and execute each statement
      const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
      
      statements.forEach(statement => {
        this.db.exec(statement + ';');
      });
      
      console.log('Database schema initialized successfully');
    } catch (error) {
      console.error('Error initializing database schema:', error);
      throw error;
    }
  }

  public close(): void {
    if (this.db) {
      this.db.close();
    }
  }

  // Utility methods for common operations
  public beginTransaction(): Database.Transaction {
    return this.db.transaction(() => {});
  }

  public backup(backupPath: string): void {
    try {
      this.db.backup(backupPath);
      console.log(`Database backed up to: ${backupPath}`);
    } catch (error) {
      console.error('Error backing up database:', error);
      throw error;
    }
  }

  public getStats(): any {
    const stats = {
      alumni: this.db.prepare('SELECT COUNT(*) as count FROM alumni').get(),
      employment: this.db.prepare('SELECT COUNT(*) as count FROM employment').get(),
      surveys: this.db.prepare('SELECT COUNT(*) as count FROM surveys').get(),
      users: this.db.prepare('SELECT COUNT(*) as count FROM users').get(),
      survey_responses: this.db.prepare('SELECT COUNT(*) as count FROM survey_responses').get(),
      reports: this.db.prepare('SELECT COUNT(*) as count FROM reports').get(),
      settings: this.db.prepare('SELECT COUNT(*) as count FROM settings').get()
    };
    
    return stats;
  }

  public vacuum(): void {
    this.db.exec('VACUUM');
    console.log('Database vacuumed successfully');
  }
}

export default DatabaseConnection;
