#!/usr/bin/env node

// <PERSON>ript to load mock data into SQLite database
// Usage: node scripts/load-mock-data.js

import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to mock data file
const MOCK_DATA_PATH = path.join(__dirname, '..', 'data', 'mock-data.json');
const SERVER_URL = 'http://localhost:3002';

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${SERVER_URL}/api/health`);
    const result = await response.json();
    return result.success;
  } catch (error) {
    return false;
  }
}

// Load mock data from file
function loadMockData() {
  try {
    if (!fs.existsSync(MOCK_DATA_PATH)) {
      throw new Error(`Mock data file not found: ${MOCK_DATA_PATH}`);
    }

    console.log(`📁 Reading mock data from ${MOCK_DATA_PATH}...`);
    const fileContent = fs.readFileSync(MOCK_DATA_PATH, 'utf8');
    const data = JSON.parse(fileContent);

    // Validate data structure
    const requiredKeys = ['alumni', 'employment', 'users', 'surveys', 'settings', 'survey_responses'];
    for (const key of requiredKeys) {
      if (!data[key] || !Array.isArray(data[key])) {
        throw new Error(`Invalid or missing data for: ${key}`);
      }
    }

    return data;
  } catch (error) {
    console.error('❌ Failed to load mock data:', error.message);
    process.exit(1);
  }
}

// Clear existing data (optional)
async function clearDatabase() {
  try {
    console.log('🗑️  Clearing existing data...');
    
    // Clear in reverse order to respect foreign key constraints
    const tables = ['survey_responses', 'employment', 'surveys', 'alumni', 'users', 'settings'];
    
    for (const table of tables) {
      const response = await fetch(`${SERVER_URL}/api/${table}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        console.warn(`⚠️  Could not clear ${table} table (this might be normal)`);
      }
    }
    
    console.log('✅ Database cleared');
  } catch (error) {
    console.warn('⚠️  Could not clear database:', error.message);
  }
}

// Migrate data to SQLite
async function migrateData(data) {
  try {
    console.log('🔄 Starting migration to SQLite...');
    console.log(`📈 Data summary:`);
    console.log(`   - Alumni: ${data.alumni?.length || 0} records`);
    console.log(`   - Employment: ${data.employment?.length || 0} records`);
    console.log(`   - Users: ${data.users?.length || 0} records`);
    console.log(`   - Surveys: ${data.surveys?.length || 0} records`);
    console.log(`   - Settings: ${data.settings?.length || 0} records`);
    console.log(`   - Survey Responses: ${data.survey_responses?.length || 0} records`);

    // Step 1: Migrate alumni first
    console.log('🔄 Step 1: Migrating alumni...');
    const alumniResponse = await fetch(`${SERVER_URL}/api/migration/from-json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ alumni: data.alumni })
    });

    const alumniResult = await alumniResponse.json();
    if (!alumniResult.success) {
      console.error('❌ Alumni migration failed:', alumniResult.message);
      return alumniResult;
    }

    // Step 2: Migrate users
    console.log('🔄 Step 2: Migrating users...');
    const usersResponse = await fetch(`${SERVER_URL}/api/migration/from-json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ users: data.users })
    });

    // Step 3: Migrate surveys
    console.log('🔄 Step 3: Migrating surveys...');
    const surveysResponse = await fetch(`${SERVER_URL}/api/migration/from-json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ surveys: data.surveys })
    });

    // Step 4: Migrate settings
    console.log('🔄 Step 4: Migrating settings...');
    const settingsResponse = await fetch(`${SERVER_URL}/api/migration/from-json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ settings: data.settings })
    });

    // Step 5: Migrate employment (after alumni) using bulk endpoint
    console.log('🔄 Step 5: Migrating employment...');
    if (data.employment && data.employment.length > 0) {
      // Convert camelCase to snake_case for database
      const employmentData = data.employment.map(emp => ({
        alumni_id: emp.alumniId,
        nama_perusahaan: emp.namaPerusahaan,
        posisi_jabatan: emp.posisiJabatan,
        jenis_usaha: emp.jenisUsaha,
        gaji_pertama: emp.gajiPertama,
        gaji_saat_ini: emp.gajiSaatIni,
        tanggal_mulai_kerja: emp.tanggalMulaiKerja,
        status_pekerjaan: emp.statusPekerjaan,
        relevansi_pekerjaan: emp.relevansiPekerjaan
      }));

      const employmentResponse = await fetch(`${SERVER_URL}/api/employment/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(employmentData)
      });

      const employmentResult = await employmentResponse.json();
      if (employmentResult.success) {
        console.log(`✅ Employment: ${employmentResult.data.count} records migrated`);
      } else {
        console.warn(`⚠️  Employment migration failed: ${employmentResult.message}`);
      }
    }

    // Step 6: Migrate survey responses (after alumni and surveys)
    console.log('🔄 Step 6: Migrating survey responses...');
    if (data.survey_responses && data.survey_responses.length > 0) {
      let responseCount = 0;
      let responseErrors = 0;

      for (const response of data.survey_responses) {
        try {
          const responseData = {
            alumni_id: response.alumniId,
            responses: response.responses
          };

          const responseResult = await fetch(`${SERVER_URL}/api/surveys/${response.surveyId}/responses`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(responseData)
          });

          if (responseResult.ok) {
            responseCount++;
          } else {
            responseErrors++;
            const errorData = await responseResult.json();
            console.warn(`⚠️  Failed to migrate response ${response.id}: ${errorData.message}`);
          }
        } catch (error) {
          responseErrors++;
          console.warn(`⚠️  Error migrating response ${response.id}: ${error.message}`);
        }
      }

      console.log(`✅ Survey Responses: ${responseCount} records migrated, ${responseErrors} errors`);
    }

    // Make final API call to get migration status
    const response = await fetch(`${SERVER_URL}/api/migration/status`);

    const result = await response.json();

    if (result.success) {
      console.log('✅ Migration completed successfully!');
      console.log('📊 Final migration status:');
      console.log(`   - Total records: ${result.data.total_records || 'Unknown'}`);
      console.log(`   - Database: ${result.data.database_type || 'SQLite'}`);
      console.log(`   - Last check: ${result.data.last_check || 'Unknown'}`);

      if (result.data.tables) {
        console.log('📊 Table statistics:');
        Object.entries(result.data.tables).forEach(([table, stats]) => {
          console.log(`   - ${table}: ${stats.count} records`);
        });
      }
    } else {
      console.error('❌ Migration status check failed:', result.message);
      // Don't exit here as the migration might have been partially successful
    }

  } catch (error) {
    console.error('❌ Migration failed with error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the SQLite server is running:');
      console.error('   cd server && npm run dev');
    }
    process.exit(1);
  }
}

// Get database statistics
async function getDatabaseStats() {
  try {
    const response = await fetch(`${SERVER_URL}/api/health`);
    const result = await response.json();
    
    if (result.success && result.stats) {
      console.log('📊 Current database statistics:');
      Object.entries(result.stats).forEach(([table, stats]) => {
        console.log(`   - ${table}: ${stats.count} records`);
      });
    }
  } catch (error) {
    console.warn('⚠️  Could not fetch database statistics');
  }
}

// Main execution
async function main() {
  console.log('🚀 Mock Data Loader for SQLite');
  console.log('==============================');

  // Check if server is running
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.error('❌ SQLite server is not running!');
    console.error('💡 Please start the server first:');
    console.error('   cd server && npm run dev');
    process.exit(1);
  }

  console.log('✅ SQLite server is running');

  // Show current database stats
  await getDatabaseStats();

  // Load mock data
  const mockData = loadMockData();

  // Ask for confirmation
  console.log('\n⚠️  This will replace existing data in the database.');
  console.log('Do you want to continue? (y/N)');
  
  // For automated scripts, you can skip this confirmation
  const shouldContinue = process.argv.includes('--force') || process.argv.includes('-f');
  
  if (!shouldContinue) {
    // In a real scenario, you'd want to use readline for user input
    // For now, we'll assume the user wants to continue if --force is not used
    console.log('💡 Use --force or -f flag to skip confirmation');
    console.log('Proceeding with migration...');
  }

  // Clear existing data (optional)
  if (process.argv.includes('--clear')) {
    await clearDatabase();
  }

  // Migrate data
  await migrateData(mockData);

  // Show final database stats
  console.log('\n📊 Final database statistics:');
  await getDatabaseStats();

  console.log('\n🎉 Mock data loaded successfully!');
  console.log('💡 You can now test the application with realistic data');
}

main().catch(console.error);
