{"alumni": [{"id": "ALM20240001", "userId": "USR001", "nim": "2024001001", "namaLengkap": "<PERSON>", "programStudi": "Teknik Informatika", "fakultas": "Teknik", "tahunMasuk": 2020, "tahunLulus": 2024, "ipk": 3.75, "email": "<EMAIL>", "noTelepon": "081234567890", "alamat": "Jakarta", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240002", "userId": "USR002", "nim": "2024001002", "namaLengkap": "<PERSON>", "programStudi": "Sistem Informasi", "fakultas": "Teknik", "tahunMasuk": 2019, "tahunLulus": 2023, "ipk": 3.85, "email": "<EMAIL>", "noTelepon": "081987654321", "alamat": "Bandung", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240003", "userId": "USR003", "nim": "2024001003", "namaLengkap": "<PERSON>", "programStudi": "Teknik Elektro", "fakultas": "Teknik", "tahunMasuk": 2018, "tahunLulus": 2022, "ipk": 3.65, "email": "<EMAIL>", "noTelepon": "081333444555", "alamat": "Surabaya", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240004", "userId": "USR004", "nim": "2024001004", "namaLengkap": "<PERSON><PERSON>", "programStudi": "Manaj<PERSON>en", "fakultas": "Ekonomi", "tahunMasuk": 2019, "tahunLulus": 2023, "ipk": 3.9, "email": "<EMAIL>", "noTelepon": "081666777888", "alamat": "Yogyakarta", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240005", "userId": "USR005", "nim": "2024001005", "namaLengkap": "<PERSON><PERSON>", "programStudi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fakultas": "Ekonomi", "tahunMasuk": 2020, "tahunLulus": 2024, "ipk": 3.55, "email": "<EMAIL>", "noTelepon": "081999000111", "alamat": "Medan", "statusVerifikasi": "pending", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240006", "userId": "USR007", "nim": "2024001006", "namaLengkap": "<PERSON>", "programStudi": "Pendidikan Bahasa Inggris", "fakultas": "Pendidikan", "tahunMasuk": 2019, "tahunLulus": 2023, "ipk": 3.7, "email": "<EMAIL>", "noTelepon": "081111222333", "alamat": "Manado", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240007", "userId": "USR008", "nim": "2024001007", "namaLengkap": "<PERSON><PERSON>", "programStudi": "Tek<PERSON>", "fakultas": "Teknik", "tahunMasuk": 2018, "tahunLulus": 2022, "ipk": 3.6, "email": "<EMAIL>", "noTelepon": "081444555666", "alamat": "Makassar", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "ALM20240008", "userId": "USR009", "nim": "2024001008", "namaLengkap": "<PERSON><PERSON>", "programStudi": "Psikologi", "fakultas": "Psikologi", "tahunMasuk": 2020, "tahunLulus": 2024, "ipk": 3.8, "email": "<EMAIL>", "noTelepon": "081777888999", "alamat": "<PERSON><PERSON><PERSON>", "statusVerifikasi": "verified", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "employment": [{"id": "EMP001", "alumniId": "ALM20240001", "namaPerusahaan": "Tech Corp", "posisiJabatan": "Software Developer", "jenisUsaha": "IT Services", "gajiPertama": 8000000, "gajiSaatIni": 12000000, "tanggalMulaiKerja": "2024-02-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2024-02-01T00:00:00.000Z", "updatedAt": "2024-02-01T00:00:00.000Z"}, {"id": "EMP002", "alumniId": "ALM20240002", "namaPerusahaan": "Digital Solutions", "posisiJabatan": "System Analyst", "jenisUsaha": "IT Consulting", "gajiPertama": 7500000, "gajiSaatIni": 11000000, "tanggalMulaiKerja": "2023-03-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "relevan", "createdAt": "2023-03-01T00:00:00.000Z", "updatedAt": "2023-03-01T00:00:00.000Z"}, {"id": "EMP003", "alumniId": "ALM20240003", "namaPerusahaan": "PT Listrik Negara", "posisiJabatan": "Electrical Engineer", "jenisUsaha": "Utilities", "gajiPertama": 9000000, "gajiSaatIni": ********, "tanggalMulaiKerja": "2022-07-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2022-07-01T00:00:00.000Z", "updatedAt": "2022-07-01T00:00:00.000Z"}, {"id": "EMP004", "alumniId": "ALM20240004", "namaPerusahaan": "Bank Mandiri", "posisiJabatan": "Management Trainee", "jenisUsaha": "Banking", "gajiPertama": 6500000, "gajiSaatIni": 9500000, "tanggalMulaiKerja": "2023-08-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "relevan", "createdAt": "2023-08-01T00:00:00.000Z", "updatedAt": "2023-08-01T00:00:00.000Z"}, {"id": "EMP005", "alumniId": "ALM20240005", "namaPerusahaan": "Wirausaha <PERSON>", "posisiJabatan": "<PERSON><PERSON><PERSON><PERSON>", "jenisUsaha": "Jasa <PERSON>i", "gajiPertama": 5000000, "gajiSaatIni": 8000000, "tanggalMulaiKerja": "2024-03-01", "statusPekerjaan": "wira<PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2024-03-01T00:00:00.000Z", "updatedAt": "2024-03-01T00:00:00.000Z"}, {"id": "EMP006", "alumniId": "ALM20240006", "namaPerusahaan": "SMA Negeri 1 Manado", "posisiJabatan": "Guru <PERSON>", "jenisUsaha": "Pendidikan", "gajiPertama": 4500000, "gajiSaatIni": 5500000, "tanggalMulaiKerja": "2023-07-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2023-07-01T00:00:00.000Z", "updatedAt": "2023-07-01T00:00:00.000Z"}, {"id": "EMP007", "alumniId": "ALM20240007", "namaPerusahaan": "PT Industri Manufaktur", "posisiJabatan": "Mechanical Engineer", "jenisUsaha": "Manufacturing", "gajiPertama": 7000000, "gajiSaatIni": 10500000, "tanggalMulaiKerja": "2022-09-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2022-09-01T00:00:00.000Z", "updatedAt": "2022-09-01T00:00:00.000Z"}, {"id": "EMP008", "alumniId": "ALM20240008", "namaPerusahaan": "Klinik Psikologi Sehat", "posisiJabatan": "Psikolog", "jenisUsaha": "Healthcare", "gajiPertama": 6000000, "gajiSaatIni": 8500000, "tanggalMulaiKerja": "2024-04-01", "statusPekerjaan": "<PERSON><PERSON><PERSON>", "relevansiPekerjaan": "sangat_relevan", "createdAt": "2024-04-01T00:00:00.000Z", "updatedAt": "2024-04-01T00:00:00.000Z"}], "users": [{"id": "USR001", "username": "john.doe", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-15T10:00:00.000Z"}, {"id": "USR002", "username": "jane.smith", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-14T15:30:00.000Z"}, {"id": "USR003", "username": "ahmad.rizki", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-12T08:45:00.000Z"}, {"id": "USR004", "username": "sari.dewi", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON><PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-16T14:20:00.000Z"}, {"id": "USR005", "username": "budi.santoso", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON><PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-10T09:15:00.000Z"}, {"id": "USR006", "username": "admin.staff", "email": "<EMAIL>", "role": "staff", "namaLengkap": "Admin Staff", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-16T16:00:00.000Z"}, {"id": "USR007", "username": "maria.sari", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-13T11:30:00.000Z"}, {"id": "USR008", "username": "andi.pratama", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON><PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-11T09:45:00.000Z"}, {"id": "USR009", "username": "de<PERSON>.lestari", "email": "<EMAIL>", "role": "alumni", "namaLengkap": "<PERSON><PERSON>", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "lastLogin": "2024-01-17T13:15:00.000Z"}], "surveys": [{"id": "SRV001", "judul": "Survey Kepuasan Alumni 2024", "deskripsi": "Survey untuk mengetahui kepuasan alumni terhadap program studi", "tanggalMulai": "2024-01-01", "tanggalSelesai": "2024-03-31", "status": "active", "targetAlumni": ["ALM20240001", "ALM20240002", "ALM20240003", "ALM20240004", "ALM20240006", "ALM20240007", "ALM20240008"], "questions": [{"id": "Q1", "type": "rating", "question": "<PERSON><PERSON><PERSON> pen<PERSON>ian Anda terhadap kualitas pendidikan?", "required": true, "options": [1, 2, 3, 4, 5]}, {"id": "Q2", "type": "text", "question": "Saran untuk perbaikan program studi", "required": false}, {"id": "Q3", "type": "multiple_choice", "question": "Aspek mana yang paling perlu ditingkatkan?", "required": true, "options": ["<PERSON><PERSON><PERSON>", "Fasilitas", "<PERSON><PERSON>", "Praktikum", "<PERSON><PERSON><PERSON>"]}], "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SRV002", "judul": "Tracer Study Alumni 2024", "deskripsi": "Survey untuk melacak karir dan pek<PERSON>jaan alumni set<PERSON><PERSON> lulus", "tanggalMulai": "2024-02-01", "tanggalSelesai": "2024-04-30", "status": "active", "targetAlumni": ["ALM20240001", "ALM20240002", "ALM20240003", "ALM20240004", "ALM20240005", "ALM20240006", "ALM20240007", "ALM20240008"], "questions": [{"id": "Q1", "type": "multiple_choice", "question": "Status pekerjaan saat ini?", "required": true, "options": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> be<PERSON>", "Melanjutkan studi", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "Q2", "type": "text", "question": "<PERSON>a <PERSON>/instansi tempat bekerja", "required": false}, {"id": "Q3", "type": "rating", "question": "<PERSON><PERSON><PERSON> relevan pekerjaan dengan bidang studi?", "required": true, "options": [1, 2, 3, 4, 5]}, {"id": "Q4", "type": "number", "question": "<PERSON><PERSON><PERSON> lama waktu tunggu untuk mendapat pekerjaan pertama? (bulan)", "required": false}], "createdAt": "2024-02-01T00:00:00.000Z", "updatedAt": "2024-02-01T00:00:00.000Z"}], "settings": [{"id": "SET001", "key": "university_name", "value": "\"Universitas Pendidikan Indonesia Manado\"", "description": "Nama universitas", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET002", "key": "contact_email", "value": "\"<EMAIL>\"", "description": "<PERSON><PERSON>", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET003", "key": "contact_phone", "value": "\"+62 431 123456\"", "description": "Nomor telepon kontak", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET004", "key": "address", "value": "\"Jl. <PERSON><PERSON>, Tondano, Minahasa, Sulawesi Utara\"", "description": "Alamat universitas", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET005", "key": "survey_reminder_days", "value": "7", "description": "Hari pengingat survey", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET006", "key": "email_notifications", "value": "true", "description": "Notifikasi email", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET007", "key": "auto_verification", "value": "false", "description": "<PERSON>erifi<PERSON><PERSON> otomatis", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "SET008", "key": "max_file_size_mb", "value": "10", "description": "Ukuran maksimal file (MB)", "updatedAt": "2024-01-01T00:00:00.000Z"}], "survey_responses": [{"id": "RESP001", "surveyId": "SRV001", "alumniId": "ALM20240001", "responses": {"Q1": 5, "Q2": "Program studi sudah baik, perlu ditingkatkan praktikum", "Q3": "Praktikum"}, "submittedAt": "2024-01-15T10:00:00.000Z"}, {"id": "RESP002", "surveyId": "SRV001", "alumniId": "ALM20240002", "responses": {"Q1": 4, "Q2": "Kurikulum perlu disesuaikan dengan kebutuhan industri", "Q3": "<PERSON><PERSON><PERSON>"}, "submittedAt": "2024-01-16T14:30:00.000Z"}, {"id": "RESP003", "surveyId": "SRV001", "alumniId": "ALM20240003", "responses": {"Q1": 4, "Q2": "Fasilitas laboratorium perlu diperbaiki", "Q3": "Fasilitas"}, "submittedAt": "2024-01-17T09:15:00.000Z"}, {"id": "RESP004", "surveyId": "SRV002", "alumniId": "ALM20240001", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "Tech Corp", "Q3": 5, "Q4": 2}, "submittedAt": "2024-02-10T09:15:00.000Z"}, {"id": "RESP005", "surveyId": "SRV002", "alumniId": "ALM20240003", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "PT Listrik Negara", "Q3": 5, "Q4": 1}, "submittedAt": "2024-02-12T11:45:00.000Z"}, {"id": "RESP006", "surveyId": "SRV002", "alumniId": "ALM20240004", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "Bank Mandiri", "Q3": 4, "Q4": 3}, "submittedAt": "2024-02-14T16:20:00.000Z"}, {"id": "RESP007", "surveyId": "SRV002", "alumniId": "ALM20240005", "responses": {"Q1": "<PERSON><PERSON><PERSON><PERSON>", "Q2": "Wirausaha <PERSON>", "Q3": 5, "Q4": 6}, "submittedAt": "2024-02-16T10:30:00.000Z"}, {"id": "RESP008", "surveyId": "SRV002", "alumniId": "ALM20240006", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "SMA Negeri 1 Manado", "Q3": 5, "Q4": 2}, "submittedAt": "2024-02-18T13:45:00.000Z"}, {"id": "RESP009", "surveyId": "SRV002", "alumniId": "ALM20240007", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "PT Industri Manufaktur", "Q3": 5, "Q4": 1}, "submittedAt": "2024-02-20T08:30:00.000Z"}, {"id": "RESP010", "surveyId": "SRV002", "alumniId": "ALM20240008", "responses": {"Q1": "<PERSON><PERSON><PERSON>", "Q2": "Klinik Psikologi Sehat", "Q3": 5, "Q4": 1}, "submittedAt": "2024-02-22T15:00:00.000Z"}]}