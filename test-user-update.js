const API_BASE_URL = 'http://localhost:3002/api';

async function testUserUpdate() {
  console.log('🧪 Testing User Update API...\n');

  try {
    // First, get the user that's causing the error
    const userId = 'USR1752566964215sdmqy';
    console.log(`1️⃣ Getting user details for ID: ${userId}...`);
    
    const getResponse = await fetch(`${API_BASE_URL}/users/${userId}`);
    const getResult = await getResponse.json();
    
    if (getResult.success) {
      console.log('✅ User found:', getResult.data.nama_lengkap);
      console.log('   Email:', getResult.data.email);
      console.log('   Role:', getResult.data.role);
      console.log('   Username:', getResult.data.username);
      
      // Now try to update the user
      console.log('\n2️⃣ Testing user update...');
      
      const updateData = {
        nama_lengkap: getResult.data.nama_lengkap,
        email: getResult.data.email,
        role: getResult.data.role,
        username: getResult.data.username
      };
      
      console.log('   Update data:', JSON.stringify(updateData, null, 2));
      
      const updateResponse = await fetch(`${API_BASE_URL}/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });
      
      const updateResult = await updateResponse.json();
      
      if (updateResult.success) {
        console.log('✅ User update successful');
        console.log('   Updated user:', updateResult.data.nama_lengkap);
      } else {
        console.log('❌ User update failed:', updateResult.message);
        console.log('   Error:', updateResult.error);
      }
      
    } else {
      console.log('❌ User not found:', getResult.message);
    }

    // Test with a simpler update
    console.log('\n3️⃣ Testing simple update...');
    const simpleUpdateData = {
      nama_lengkap: 'AGUSTINUS MODEONG (Updated)'
    };
    
    const simpleResponse = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(simpleUpdateData),
    });
    
    const simpleResult = await simpleResponse.json();
    
    if (simpleResult.success) {
      console.log('✅ Simple update successful');
    } else {
      console.log('❌ Simple update failed:', simpleResult.message);
      console.log('   Error:', simpleResult.error);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testUserUpdate();
