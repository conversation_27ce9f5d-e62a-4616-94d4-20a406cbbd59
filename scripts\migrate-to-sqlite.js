#!/usr/bin/env node

// Migration script to move data from localStorage JSON files to SQLite
// Usage: node scripts/migrate-to-sqlite.js [backup-file.json]

import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';

// Sample data structure that would typically be in localStorage
const sampleData = {
  alumni: [
    {
      id: 'ALM20240001',
      userId: 'USR001',
      nim: '123456789',
      namaLengkap: '<PERSON>',
      programStudi: 'Teknik Informatika',
      fakultas: 'Teknik',
      tahunMasuk: 2020,
      tahunLulus: 2024,
      ipk: 3.75,
      email: '<EMAIL>',
      noTelepon: '081234567890',
      alamat: 'Jakarta',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'ALM20240002',
      userId: 'USR002',
      nim: '987654321',
      namaLengkap: '<PERSON>',
      programStudi: 'Sistem Informasi',
      fakultas: 'Teknik',
      tahunMasuk: 2019,
      tahunLulus: 2023,
      ipk: 3.85,
      email: '<EMAIL>',
      noTelepon: '081987654321',
      alamat: 'Bandung',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'ALM20240003',
      userId: 'USR003',
      nim: '111222333',
      namaLengkap: 'Ahmad Rizki',
      programStudi: 'Teknik Elektro',
      fakultas: 'Teknik',
      tahunMasuk: 2018,
      tahunLulus: 2022,
      ipk: 3.65,
      email: '<EMAIL>',
      noTelepon: '081333444555',
      alamat: 'Surabaya',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'ALM20240004',
      userId: 'USR004',
      nim: '444555666',
      namaLengkap: 'Sari Dewi',
      programStudi: 'Manajemen',
      fakultas: 'Ekonomi',
      tahunMasuk: 2019,
      tahunLulus: 2023,
      ipk: 3.90,
      email: '<EMAIL>',
      noTelepon: '081666777888',
      alamat: 'Yogyakarta',
      statusVerifikasi: 'verified',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'ALM20240005',
      userId: 'USR005',
      nim: '777888999',
      namaLengkap: 'Budi Santoso',
      programStudi: 'Akuntansi',
      fakultas: 'Ekonomi',
      tahunMasuk: 2020,
      tahunLulus: 2024,
      ipk: 3.55,
      email: '<EMAIL>',
      noTelepon: '081999000111',
      alamat: 'Medan',
      statusVerifikasi: 'pending',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ],
  employment: [
    {
      id: 'EMP001',
      alumniId: 'ALM20240001',
      namaPerusahaan: 'Tech Corp',
      posisiJabatan: 'Software Developer',
      jenisUsaha: 'IT Services',
      gajiPertama: 8000000,
      gajiSaatIni: 12000000,
      tanggalMulaiKerja: '2024-02-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'sangat_relevan',
      createdAt: '2024-02-01T00:00:00.000Z',
      updatedAt: '2024-02-01T00:00:00.000Z'
    },
    {
      id: 'EMP002',
      alumniId: 'ALM20240002',
      namaPerusahaan: 'Digital Solutions',
      posisiJabatan: 'System Analyst',
      jenisUsaha: 'IT Consulting',
      gajiPertama: 7500000,
      gajiSaatIni: 11000000,
      tanggalMulaiKerja: '2023-03-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'relevan',
      createdAt: '2023-03-01T00:00:00.000Z',
      updatedAt: '2023-03-01T00:00:00.000Z'
    },
    {
      id: 'EMP003',
      alumniId: 'ALM20240003',
      namaPerusahaan: 'PT Listrik Negara',
      posisiJabatan: 'Electrical Engineer',
      jenisUsaha: 'Utilities',
      gajiPertama: 9000000,
      gajiSaatIni: ********,
      tanggalMulaiKerja: '2022-07-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'sangat_relevan',
      createdAt: '2022-07-01T00:00:00.000Z',
      updatedAt: '2022-07-01T00:00:00.000Z'
    },
    {
      id: 'EMP004',
      alumniId: 'ALM20240004',
      namaPerusahaan: 'Bank Mandiri',
      posisiJabatan: 'Management Trainee',
      jenisUsaha: 'Banking',
      gajiPertama: 6500000,
      gajiSaatIni: 9500000,
      tanggalMulaiKerja: '2023-08-01',
      statusPekerjaan: 'bekerja',
      relevansiPekerjaan: 'relevan',
      createdAt: '2023-08-01T00:00:00.000Z',
      updatedAt: '2023-08-01T00:00:00.000Z'
    },
    {
      id: 'EMP005',
      alumniId: 'ALM20240005',
      namaPerusahaan: 'Wirausaha Sendiri',
      posisiJabatan: 'Pemilik Usaha',
      jenisUsaha: 'Jasa Akuntansi',
      gajiPertama: 5000000,
      gajiSaatIni: 8000000,
      tanggalMulaiKerja: '2024-03-01',
      statusPekerjaan: 'wirausaha',
      relevansiPekerjaan: 'sangat_relevan',
      createdAt: '2024-03-01T00:00:00.000Z',
      updatedAt: '2024-03-01T00:00:00.000Z'
    }
  ],
  users: [
    {
      id: 'USR001',
      username: 'john.doe',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'John Doe',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-15T10:00:00.000Z'
    },
    {
      id: 'USR002',
      username: 'jane.smith',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'Jane Smith',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-14T15:30:00.000Z'
    },
    {
      id: 'USR003',
      username: 'ahmad.rizki',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'Ahmad Rizki',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-12T08:45:00.000Z'
    },
    {
      id: 'USR004',
      username: 'sari.dewi',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'Sari Dewi',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-16T14:20:00.000Z'
    },
    {
      id: 'USR005',
      username: 'budi.santoso',
      email: '<EMAIL>',
      role: 'alumni',
      namaLengkap: 'Budi Santoso',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-10T09:15:00.000Z'
    },
    {
      id: 'USR006',
      username: 'admin.staff',
      email: '<EMAIL>',
      role: 'staff',
      namaLengkap: 'Admin Staff',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      lastLogin: '2024-01-16T16:00:00.000Z'
    }
  ],
  surveys: [
    {
      id: 'SRV001',
      judul: 'Survey Kepuasan Alumni 2024',
      deskripsi: 'Survey untuk mengetahui kepuasan alumni terhadap program studi',
      tanggalMulai: '2024-01-01',
      tanggalSelesai: '2024-03-31',
      status: 'active',
      targetAlumni: ['ALM20240001', 'ALM20240002', 'ALM20240003', 'ALM20240004'],
      questions: [
        {
          id: 'Q1',
          type: 'rating',
          question: 'Bagaimana penilaian Anda terhadap kualitas pendidikan?',
          required: true,
          options: [1, 2, 3, 4, 5]
        },
        {
          id: 'Q2',
          type: 'text',
          question: 'Saran untuk perbaikan program studi',
          required: false
        },
        {
          id: 'Q3',
          type: 'multiple_choice',
          question: 'Aspek mana yang paling perlu ditingkatkan?',
          required: true,
          options: ['Kurikulum', 'Fasilitas', 'Dosen', 'Praktikum', 'Lainnya']
        }
      ],
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SRV002',
      judul: 'Tracer Study Alumni 2024',
      deskripsi: 'Survey untuk melacak karir dan pekerjaan alumni setelah lulus',
      tanggalMulai: '2024-02-01',
      tanggalSelesai: '2024-04-30',
      status: 'active',
      targetAlumni: ['ALM20240001', 'ALM20240002', 'ALM20240003', 'ALM20240004', 'ALM20240005'],
      questions: [
        {
          id: 'Q1',
          type: 'multiple_choice',
          question: 'Status pekerjaan saat ini?',
          required: true,
          options: ['Bekerja', 'Belum bekerja', 'Melanjutkan studi', 'Wirausaha']
        },
        {
          id: 'Q2',
          type: 'text',
          question: 'Nama perusahaan/instansi tempat bekerja',
          required: false
        },
        {
          id: 'Q3',
          type: 'rating',
          question: 'Seberapa relevan pekerjaan dengan bidang studi?',
          required: true,
          options: [1, 2, 3, 4, 5]
        },
        {
          id: 'Q4',
          type: 'number',
          question: 'Berapa lama waktu tunggu untuk mendapat pekerjaan pertama? (bulan)',
          required: false
        }
      ],
      createdAt: '2024-02-01T00:00:00.000Z',
      updatedAt: '2024-02-01T00:00:00.000Z'
    }
  ],
  settings: [
    {
      id: 'SET001',
      key: 'university_name',
      value: 'Universitas Pendidikan Indonesia Manado',
      description: 'Nama universitas',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SET002',
      key: 'contact_email',
      value: '<EMAIL>',
      description: 'Email kontak',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SET003',
      key: 'contact_phone',
      value: '+62 431 123456',
      description: 'Nomor telepon kontak',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SET004',
      key: 'address',
      value: 'Jl. Raya Tondano, Tondano, Minahasa, Sulawesi Utara',
      description: 'Alamat universitas',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 'SET005',
      key: 'survey_reminder_days',
      value: '7',
      description: 'Hari pengingat survey',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ],
  survey_responses: [
    {
      id: 'RESP001',
      surveyId: 'SRV001',
      alumniId: 'ALM20240001',
      responses: {
        Q1: 5,
        Q2: 'Program studi sudah baik, perlu ditingkatkan praktikum',
        Q3: 'Praktikum'
      },
      submittedAt: '2024-01-15T10:00:00.000Z'
    },
    {
      id: 'RESP002',
      surveyId: 'SRV001',
      alumniId: 'ALM20240002',
      responses: {
        Q1: 4,
        Q2: 'Kurikulum perlu disesuaikan dengan kebutuhan industri',
        Q3: 'Kurikulum'
      },
      submittedAt: '2024-01-16T14:30:00.000Z'
    },
    {
      id: 'RESP003',
      surveyId: 'SRV002',
      alumniId: 'ALM20240001',
      responses: {
        Q1: 'Bekerja',
        Q2: 'Tech Corp',
        Q3: 5,
        Q4: 2
      },
      submittedAt: '2024-02-10T09:15:00.000Z'
    },
    {
      id: 'RESP004',
      surveyId: 'SRV002',
      alumniId: 'ALM20240003',
      responses: {
        Q1: 'Bekerja',
        Q2: 'PT Listrik Negara',
        Q3: 5,
        Q4: 1
      },
      submittedAt: '2024-02-12T11:45:00.000Z'
    },
    {
      id: 'RESP005',
      surveyId: 'SRV002',
      alumniId: 'ALM20240004',
      responses: {
        Q1: 'Bekerja',
        Q2: 'Bank Mandiri',
        Q3: 4,
        Q4: 3
      },
      submittedAt: '2024-02-14T16:20:00.000Z'
    }
  ]
};

async function migrateData(dataFile) {
  try {
    let data = sampleData;
    
    // If backup file is provided, read from it
    if (dataFile && fs.existsSync(dataFile)) {
      console.log(`📁 Reading data from ${dataFile}...`);
      const fileContent = fs.readFileSync(dataFile, 'utf8');
      data = JSON.parse(fileContent);
    } else {
      console.log('📊 Using sample data for migration...');
    }

    console.log('🔄 Starting migration to SQLite...');
    console.log(`📈 Data summary:`);
    console.log(`   - Alumni: ${data.alumni?.length || 0} records`);
    console.log(`   - Employment: ${data.employment?.length || 0} records`);
    console.log(`   - Users: ${data.users?.length || 0} records`);
    console.log(`   - Surveys: ${data.surveys?.length || 0} records`);
    console.log(`   - Settings: ${data.settings?.length || 0} records`);
    console.log(`   - Survey Responses: ${data.survey_responses?.length || 0} records`);

    // Make API call to migration endpoint
    const response = await fetch('http://localhost:3002/api/migration/from-json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Migration completed successfully!');
      console.log('📊 Migration results:');
      console.log(`   - Alumni: ${result.data.alumni} records migrated`);
      console.log(`   - Employment: ${result.data.employment} records migrated`);
      console.log(`   - Users: ${result.data.users} records migrated`);
      console.log(`   - Surveys: ${result.data.surveys} records migrated`);
      console.log(`   - Settings: ${result.data.settings} records migrated`);
      console.log(`   - Survey Responses: ${result.data.survey_responses} records migrated`);
      
      if (result.data.errors && result.data.errors.length > 0) {
        console.log('⚠️  Errors encountered:');
        result.data.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.error('❌ Migration failed:', result.message);
      if (result.error) {
        console.error('Error details:', result.error);
      }
    }

  } catch (error) {
    console.error('❌ Migration failed with error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Make sure the SQLite server is running:');
      console.error('   npm run server:dev');
    }
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3002/api/health');
    const result = await response.json();
    return result.success;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  console.log('🚀 SQLite Migration Tool');
  console.log('========================');

  // Check if server is running
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.error('❌ SQLite server is not running!');
    console.error('💡 Please start the server first:');
    console.error('   npm run server:dev');
    process.exit(1);
  }

  console.log('✅ SQLite server is running');

  // Get backup file from command line argument
  const backupFile = process.argv[2];
  
  if (backupFile && !fs.existsSync(backupFile)) {
    console.error(`❌ Backup file not found: ${backupFile}`);
    process.exit(1);
  }

  await migrateData(backupFile);
}

main().catch(console.error);
