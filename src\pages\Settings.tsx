import React, { useState, useEffect } from 'react';
import { Save, User, Bell, Database, Shield, Mail, Globe, RefreshCw, Download, Upload, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import Layout from '@/components/Layout';
import { dataManager, SettingsData } from '@/utils/dataManager';

const Settings = () => {
  const [settings, setSettings] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState(false);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [isBackupDialogOpen, setIsBackupDialogOpen] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    const settingsData = dataManager.getSettingsData();
    const settingsObj: { [key: string]: any } = {};
    settingsData.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    // Set default values for settings that don't exist in database
    const defaultSettings = {
      university_name: 'Universitas Alumni Connect',
      contact_email: '<EMAIL>',
      contact_phone: '+62 21 1234567',
      address: 'Jl. Pendidikan No. 123, Jakarta',
      survey_reminder_days: 7,
      email_notifications: true,
      sms_notifications: false,
      auto_verification: false,
      backup_frequency: 'daily',
      data_retention_months: 60,
      max_file_size_mb: 10,
      allowed_file_types: 'pdf,doc,docx,jpg,png,xlsx,csv',
      session_duration_minutes: 60,
      two_factor_auth: false,
      activity_logging: true,
      max_login_attempts: 5,
      theme: 'light',
      language: 'id',
      timezone: 'Asia/Jakarta',
      date_format: 'dd/mm/yyyy',
      currency: 'IDR',
      items_per_page: 10
    };

    // Merge defaults with existing settings
    const mergedSettings = { ...defaultSettings, ...settingsObj };
    setSettings(mergedSettings);

    // Save any new default settings to database
    Object.keys(defaultSettings).forEach(key => {
      if (!(key in settingsObj)) {
        dataManager.updateSetting(key, defaultSettings[key]);
      }
    });
  };

  const handleSave = async (category: string) => {
    setLoading(true);
    try {
      // Save settings to dataManager
      Object.keys(settings).forEach(key => {
        dataManager.updateSetting(key, settings[key]);
      });
      
      alert(`Pengaturan ${category} berhasil disimpan!`);
    } catch (error) {
      alert('Gagal menyimpan pengaturan!');
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleResetSettings = () => {
    setLoading(true);
    try {
      // Clear all settings from database
      dataManager.clearAllData();

      // Reload default settings
      loadSettings();

      setIsResetDialogOpen(false);
      alert('Pengaturan berhasil direset ke default!');
    } catch (error) {
      alert('Gagal mereset pengaturan!');
    } finally {
      setLoading(false);
    }
  };

  const handleBackupData = () => {
    try {
      const allData = {
        alumni: dataManager.getAlumniData(),
        employment: dataManager.getEmploymentData(),
        surveys: dataManager.getSurveyData(),
        users: dataManager.getUserData(),
        settings: dataManager.getSettingsData(),
        timestamp: new Date().toISOString()
      };

      const dataStr = JSON.stringify(allData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `alumni-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      alert('Backup data berhasil diunduh!');
    } catch (error) {
      alert('Gagal membuat backup data!');
    }
  };

  const handleRestoreData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);

        // Validate data structure
        if (data.alumni && data.employment && data.surveys && data.users && data.settings) {
          // Restore data
          localStorage.setItem('alumni', JSON.stringify(data.alumni));
          localStorage.setItem('employment', JSON.stringify(data.employment));
          localStorage.setItem('surveys', JSON.stringify(data.surveys));
          localStorage.setItem('users', JSON.stringify(data.users));
          localStorage.setItem('settings', JSON.stringify(data.settings));

          // Reload settings
          loadSettings();

          alert('Data berhasil dipulihkan dari backup!');
        } else {
          alert('Format file backup tidak valid!');
        }
      } catch (error) {
        alert('Gagal membaca file backup!');
      }
    };
    reader.readAsText(file);
  };

  const handleTestNotification = (type: string) => {
    if (type === 'email' && settings.email_notifications) {
      alert('Test notifikasi email berhasil dikirim!');
    } else if (type === 'sms' && settings.sms_notifications) {
      alert('Test notifikasi SMS berhasil dikirim!');
    } else {
      alert(`Notifikasi ${type} tidak aktif!`);
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Pengaturan</h1>
        <p className="text-gray-600">Kelola konfigurasi sistem dan preferensi aplikasi</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <CardTitle>Pengaturan Umum</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Nama Universitas</label>
              <Input
                value={settings.university_name || ''}
                onChange={(e) => updateSetting('university_name', e.target.value)}
                placeholder="Masukkan nama universitas"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Email Kontak</label>
              <Input
                type="email"
                value={settings.contact_email || ''}
                onChange={(e) => updateSetting('contact_email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Nomor Telepon</label>
              <Input
                value={settings.contact_phone || ''}
                onChange={(e) => updateSetting('contact_phone', e.target.value)}
                placeholder="+62 21 1234567"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Alamat</label>
              <Textarea
                value={settings.address || ''}
                onChange={(e) => updateSetting('address', e.target.value)}
                placeholder="Alamat lengkap universitas"
                rows={3}
              />
            </div>
            
            <Button onClick={() => handleSave('Umum')} disabled={loading} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Simpan Pengaturan Umum
            </Button>
          </CardContent>
        </Card>

        {/* Display Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <CardTitle>Pengaturan Tampilan</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Tema</label>
              <Select
                value={settings.theme || 'light'}
                onValueChange={(value) => updateSetting('theme', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                  <SelectItem value="auto">Auto</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Bahasa</label>
              <Select
                value={settings.language || 'id'}
                onValueChange={(value) => updateSetting('language', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="id">Bahasa Indonesia</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Zona Waktu</label>
              <Select
                value={settings.timezone || 'Asia/Jakarta'}
                onValueChange={(value) => updateSetting('timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Asia/Jakarta">WIB (Jakarta)</SelectItem>
                  <SelectItem value="Asia/Makassar">WITA (Makassar)</SelectItem>
                  <SelectItem value="Asia/Jayapura">WIT (Jayapura)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Format Tanggal</label>
              <Select
                value={settings.date_format || 'dd/mm/yyyy'}
                onValueChange={(value) => updateSetting('date_format', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dd/mm/yyyy">DD/MM/YYYY</SelectItem>
                  <SelectItem value="mm/dd/yyyy">MM/DD/YYYY</SelectItem>
                  <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Mata Uang</label>
              <Select
                value={settings.currency || 'IDR'}
                onValueChange={(value) => updateSetting('currency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="IDR">Rupiah (IDR)</SelectItem>
                  <SelectItem value="USD">US Dollar (USD)</SelectItem>
                  <SelectItem value="EUR">Euro (EUR)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Item per Halaman
              </label>
              <Select
                value={settings.items_per_page?.toString() || '10'}
                onValueChange={(value) => updateSetting('items_per_page', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={() => handleSave('Tampilan')} disabled={loading} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Simpan Pengaturan Tampilan
            </Button>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <CardTitle>Pengaturan Notifikasi</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Pengingat Survey (hari)
              </label>
              <Input
                type="number"
                value={settings.survey_reminder_days || 7}
                onChange={(e) => updateSetting('survey_reminder_days', parseInt(e.target.value))}
                min="1"
                max="30"
              />
              <p className="text-xs text-gray-500 mt-1">
                Kirim pengingat survey setiap X hari
              </p>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Notifikasi Email</label>
                <p className="text-xs text-gray-500">Kirim notifikasi melalui email</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestNotification('email')}
                  disabled={!settings.email_notifications}
                >
                  Test
                </Button>
                <Switch
                  checked={settings.email_notifications || false}
                  onCheckedChange={(checked) => updateSetting('email_notifications', checked)}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Notifikasi SMS</label>
                <p className="text-xs text-gray-500">Kirim notifikasi melalui SMS</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestNotification('sms')}
                  disabled={!settings.sms_notifications}
                >
                  Test
                </Button>
                <Switch
                  checked={settings.sms_notifications || false}
                  onCheckedChange={(checked) => updateSetting('sms_notifications', checked)}
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Verifikasi Otomatis</label>
                <p className="text-xs text-gray-500">Verifikasi alumni secara otomatis</p>
              </div>
              <Switch
                checked={settings.auto_verification || false}
                onCheckedChange={(checked) => updateSetting('auto_verification', checked)}
              />
            </div>
            
            <Button onClick={() => handleSave('Notifikasi')} disabled={loading} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Simpan Pengaturan Notifikasi
            </Button>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <CardTitle>Manajemen Data</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Frekuensi Backup
              </label>
              <Select 
                value={settings.backup_frequency || 'daily'} 
                onValueChange={(value) => updateSetting('backup_frequency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Harian</SelectItem>
                  <SelectItem value="weekly">Mingguan</SelectItem>
                  <SelectItem value="monthly">Bulanan</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Retensi Data (bulan)
              </label>
              <Input
                type="number"
                value={settings.data_retention_months || 60}
                onChange={(e) => updateSetting('data_retention_months', parseInt(e.target.value))}
                min="12"
                max="120"
              />
              <p className="text-xs text-gray-500 mt-1">
                Berapa lama data disimpan sebelum dihapus
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Ukuran File Maksimal (MB)
              </label>
              <Input
                type="number"
                value={settings.max_file_size_mb || 10}
                onChange={(e) => updateSetting('max_file_size_mb', parseInt(e.target.value))}
                min="1"
                max="100"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Tipe File yang Diizinkan
              </label>
              <Input
                value={settings.allowed_file_types || ''}
                onChange={(e) => updateSetting('allowed_file_types', e.target.value)}
                placeholder="pdf,doc,docx,jpg,png"
              />
              <p className="text-xs text-gray-500 mt-1">
                Pisahkan dengan koma
              </p>
            </div>
            
            <div className="flex space-x-2">
              <Button onClick={handleBackupData} variant="outline" className="flex-1">
                <Download className="h-4 w-4 mr-2" />
                Backup Data
              </Button>
              <Dialog open={isBackupDialogOpen} onOpenChange={setIsBackupDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="flex-1">
                    <Upload className="h-4 w-4 mr-2" />
                    Restore Data
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Restore Data dari Backup</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-2 block">
                        Pilih File Backup:
                      </label>
                      <Input
                        type="file"
                        accept=".json"
                        onChange={handleRestoreData}
                        className="mt-1"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Hanya file backup JSON yang valid
                      </p>
                    </div>
                    <div className="bg-yellow-50 p-3 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        ⚠️ Peringatan: Restore akan mengganti semua data yang ada!
                      </p>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <Button onClick={() => handleSave('Data')} disabled={loading} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Simpan Pengaturan Data
            </Button>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <CardTitle>Pengaturan Keamanan</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Durasi Sesi Login (menit)
              </label>
              <Input
                type="number"
                value={settings.session_duration_minutes || 60}
                onChange={(e) => updateSetting('session_duration_minutes', parseInt(e.target.value))}
                min="15"
                max="480"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Two-Factor Authentication</label>
                <p className="text-xs text-gray-500">Aktifkan 2FA untuk keamanan tambahan</p>
              </div>
              <Switch
                checked={settings.two_factor_auth || false}
                onCheckedChange={(checked) => updateSetting('two_factor_auth', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">Log Aktivitas</label>
                <p className="text-xs text-gray-500">Catat semua aktivitas pengguna</p>
              </div>
              <Switch
                checked={settings.activity_logging || true}
                onCheckedChange={(checked) => updateSetting('activity_logging', checked)}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Maksimal Percobaan Login
              </label>
              <Input
                type="number"
                value={settings.max_login_attempts || 5}
                onChange={(e) => updateSetting('max_login_attempts', parseInt(e.target.value))}
                min="3"
                max="10"
              />
            </div>
            
            <Button onClick={() => handleSave('Keamanan')} disabled={loading} className="w-full">
              <Save className="h-4 w-4 mr-2" />
              Simpan Pengaturan Keamanan
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Reset Settings */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600 flex items-center">
            <Trash2 className="h-5 w-5 mr-2" />
            Reset Pengaturan
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Reset semua pengaturan ke nilai default. Tindakan ini tidak dapat dibatalkan.
            </p>
            <Dialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset ke Default
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Konfirmasi Reset Pengaturan</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="bg-red-50 p-3 rounded-lg">
                    <p className="text-sm text-red-800">
                      ⚠️ Peringatan: Semua pengaturan akan dikembalikan ke nilai default!
                    </p>
                  </div>
                  <p className="text-sm text-gray-600">
                    Apakah Anda yakin ingin mereset semua pengaturan? Tindakan ini tidak dapat dibatalkan.
                  </p>
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => setIsResetDialogOpen(false)} className="flex-1">
                      Batal
                    </Button>
                    <Button variant="destructive" onClick={handleResetSettings} disabled={loading} className="flex-1">
                      Ya, Reset
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* System Info */}
      <Card>
        <CardHeader>
          <CardTitle>Informasi Sistem</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Versi Aplikasi:</span>
              <p className="font-medium">v1.0.0</p>
            </div>
            <div>
              <span className="text-gray-500">Database:</span>
              <p className="font-medium">JSON LocalStorage</p>
            </div>
            <div>
              <span className="text-gray-500">Total Alumni:</span>
              <p className="font-medium">{dataManager.getAlumniData().length}</p>
            </div>
            <div>
              <span className="text-gray-500">Total Employment:</span>
              <p className="font-medium">{dataManager.getEmploymentData().length}</p>
            </div>
            <div>
              <span className="text-gray-500">Total Survey:</span>
              <p className="font-medium">{dataManager.getSurveyData().length}</p>
            </div>
            <div>
              <span className="text-gray-500">Total Users:</span>
              <p className="font-medium">{dataManager.getUserData().length}</p>
            </div>
            <div>
              <span className="text-gray-500">Storage Used:</span>
              <p className="font-medium">
                {Math.round(JSON.stringify(localStorage).length / 1024)} KB
              </p>
            </div>
            <div>
              <span className="text-gray-500">Last Update:</span>
              <p className="font-medium">{new Date().toLocaleDateString('id-ID')}</p>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Aplikasi
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (confirm('Hapus semua data cache browser?')) {
                    localStorage.clear();
                    window.location.reload();
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Cache
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const info = `
Versi: v1.0.0
Database: JSON LocalStorage
Alumni: ${dataManager.getAlumniData().length}
Employment: ${dataManager.getEmploymentData().length}
Survey: ${dataManager.getSurveyData().length}
Users: ${dataManager.getUserData().length}
Storage: ${Math.round(JSON.stringify(localStorage).length / 1024)} KB
Browser: ${navigator.userAgent}
                  `;
                  navigator.clipboard.writeText(info);
                  alert('Informasi sistem disalin ke clipboard!');
                }}
              >
                <Database className="h-4 w-4 mr-2" />
                Copy System Info
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </Layout>
  );
};

export default Settings;
