// Comprehensive test for real alumni integration
const API_BASE_URL = 'http://localhost:3002/api';

async function testRealAlumniIntegration() {
  console.log('🧪 Testing Real Alumni Integration...\n');

  try {
    // 1. Check real alumni data
    console.log('1️⃣ Checking real alumni data...');
    const alumniResponse = await fetch(`${API_BASE_URL}/alumni`);
    const alumniResult = await alumniResponse.json();
    
    if (alumniResult.success) {
      const realAlumni = alumniResult.data.filter(a => 
        a.nama_lengkap.includes('AGUSTINA') || 
        a.nama_lengkap.includes('AGUSTINUS') || 
        a.nama_lengkap.includes('AHMAD SHODIQUN')
      );
      
      console.log('✅ Real alumni in database:', realAlumni.length);
      realAlumni.forEach(alumni => {
        console.log(`   - ${alumni.nim}: ${alumni.nama_lengkap} (${alumni.email})`);
      });
    }

    // 2. Check users created from real alumni
    console.log('\n2️⃣ Checking users created from real alumni...');
    const usersResponse = await fetch(`${API_BASE_URL}/users`);
    const usersResult = await usersResponse.json();
    
    if (usersResult.success) {
      const realAlumniUsers = usersResult.data.filter(u => 
        u.username === '185420110' || 
        u.username === '01210014' || 
        u.username === '195420110' || 
        u.username === '9210058'
      );
      
      console.log('✅ Real alumni users:', realAlumniUsers.length);
      realAlumniUsers.forEach(user => {
        console.log(`   - ${user.username}: ${user.nama_lengkap} (${user.email})`);
      });
    }

    // 3. Test authentication for each real alumni
    console.log('\n3️⃣ Testing authentication for real alumni...');
    const testCredentials = [
      { email: '<EMAIL>', password: '185420110', name: 'AGUSTINA MURIP' },
      { email: '<EMAIL>', password: '01210014', name: 'AGUSTINA SALOSSA' },
      { email: '<EMAIL>', password: '195420110', name: 'AGUSTINUS MODEONG' },
      { email: '<EMAIL>', password: '9210058', name: 'AHMAD SHODIQUN' }
    ];

    let successfulLogins = 0;
    for (const cred of testCredentials) {
      try {
        const authResponse = await fetch(`${API_BASE_URL}/users/authenticate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: cred.email,
            password: cred.password
          }),
        });

        const authResult = await authResponse.json();
        
        if (authResult.success) {
          console.log(`✅ ${cred.name}: Login successful`);
          successfulLogins++;
        } else {
          console.log(`❌ ${cred.name}: Login failed - ${authResult.message}`);
        }
      } catch (error) {
        console.log(`❌ ${cred.name}: Error - ${error.message}`);
      }
    }

    // 4. Check user-alumni linking
    console.log('\n4️⃣ Checking user-alumni linking...');
    for (const cred of testCredentials) {
      try {
        // Find the user
        const user = usersResult.data.find(u => u.email === cred.email);
        if (user) {
          // Find the corresponding alumni
          const alumni = alumniResult.data.find(a => a.email === cred.email);
          if (alumni && alumni.user_id === user.id) {
            console.log(`✅ ${cred.name}: User-Alumni linking correct`);
          } else {
            console.log(`❌ ${cred.name}: User-Alumni linking incorrect`);
          }
        }
      } catch (error) {
        console.log(`❌ ${cred.name}: Linking check error - ${error.message}`);
      }
    }

    // 5. Summary
    console.log('\n📊 Test Summary:');
    const realAlumniCount = alumniResult.success ?
      alumniResult.data.filter(a =>
        a.nama_lengkap.includes('AGUSTINA') ||
        a.nama_lengkap.includes('AGUSTINUS') ||
        a.nama_lengkap.includes('AHMAD SHODIQUN')
      ).length : 0;

    const realAlumniUsersCount = usersResult.success ?
      usersResult.data.filter(u =>
        u.username === '185420110' ||
        u.username === '01210014' ||
        u.username === '195420110' ||
        u.username === '9210058'
      ).length : 0;

    console.log(`   - Real alumni in database: ${realAlumniCount}`);
    console.log(`   - Users created from real alumni: ${realAlumniUsersCount}`);
    console.log(`   - Successful logins: ${successfulLogins}/${testCredentials.length}`);

    if (successfulLogins === testCredentials.length) {
      console.log('\n🎉 All tests passed! Real alumni integration is working correctly.');
      console.log('\n📋 Alumni can now login with:');
      testCredentials.forEach(cred => {
        console.log(`   - Email: ${cred.email}, Password: ${cred.password}`);
      });
    } else {
      console.log('\n⚠️ Some tests failed. Please check the issues above.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRealAlumniIntegration();
