// Migration Helper - Migrate data from localStorage to SQLite

import { dataManager } from './dataManager';
import { sqliteDataManager } from './sqliteDataManager';
import apiClient from './apiClient';

export interface MigrationResult {
  success: boolean;
  message: string;
  details: {
    alumni: number;
    employment: number;
    users: number;
    surveys: number;
    settings: number;
    survey_responses: number;
    errors: string[];
  };
}

export class MigrationHelper {
  async checkServerStatus(): Promise<boolean> {
    try {
      const response = await apiClient.healthCheck();
      return response.success;
    } catch (error) {
      console.error('Server not available:', error);
      return false;
    }
  }

  async getLocalStorageData(): Promise<any> {
    return {
      alumni: dataManager.getAlumniData(),
      employment: dataManager.getEmploymentData(),
      users: dataManager.getUserData(),
      surveys: dataManager.getSurveyData(),
      settings: dataManager.getSettingsData(),
      survey_responses: dataManager.getSurveyResponses()
    };
  }

  async migrateToSQLite(): Promise<MigrationResult> {
    try {
      // Check if server is available
      const serverAvailable = await this.checkServerStatus();
      if (!serverAvailable) {
        return {
          success: false,
          message: 'SQLite server is not available. Please start the server first.',
          details: {
            alumni: 0,
            employment: 0,
            users: 0,
            surveys: 0,
            settings: 0,
            survey_responses: 0,
            errors: ['Server not available']
          }
        };
      }

      // Get data from localStorage
      const localData = await this.getLocalStorageData();
      
      // Check if there's data to migrate
      const totalRecords = Object.values(localData).reduce((sum: number, data: any) => {
        return sum + (Array.isArray(data) ? data.length : 0);
      }, 0);

      if (totalRecords === 0) {
        return {
          success: true,
          message: 'No data found in localStorage to migrate.',
          details: {
            alumni: 0,
            employment: 0,
            users: 0,
            surveys: 0,
            settings: 0,
            survey_responses: 0,
            errors: []
          }
        };
      }

      // Perform migration
      const response = await apiClient.migrateFromJson(localData);
      
      if (response.success) {
        return {
          success: true,
          message: 'Data migration completed successfully!',
          details: response.data || {
            alumni: 0,
            employment: 0,
            users: 0,
            surveys: 0,
            settings: 0,
            survey_responses: 0,
            errors: []
          }
        };
      } else {
        return {
          success: false,
          message: response.message || 'Migration failed',
          details: {
            alumni: 0,
            employment: 0,
            users: 0,
            surveys: 0,
            settings: 0,
            survey_responses: 0,
            errors: [response.error || 'Unknown error']
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: 'Migration failed with error',
        details: {
          alumni: 0,
          employment: 0,
          users: 0,
          surveys: 0,
          settings: 0,
          survey_responses: 0,
          errors: [error instanceof Error ? error.message : 'Unknown error']
        }
      };
    }
  }

  async createBackup(): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const localData = await this.getLocalStorageData();
      const backup = {
        ...localData,
        metadata: {
          created_at: new Date().toISOString(),
          source: 'localStorage',
          version: '1.0.0'
        }
      };

      // Create downloadable backup file
      const blob = new Blob([JSON.stringify(backup, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tracer_backup_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      return {
        success: true,
        data: backup
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Backup failed'
      };
    }
  }

  async clearLocalStorage(): Promise<void> {
    const keys = [
      'tracer_alumni',
      'tracer_employment', 
      'tracer_users',
      'tracer_surveys',
      'tracer_settings',
      'tracer_survey_responses'
    ];

    keys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('localStorage cleared');
  }

  async validateMigration(): Promise<{
    success: boolean;
    comparison: {
      localStorage: any;
      sqlite: any;
      matches: boolean;
    };
  }> {
    try {
      // Get data from both sources
      const localData = await this.getLocalStorageData();
      
      // Get data from SQLite
      const sqliteData = {
        alumni: await sqliteDataManager.getAlumniData(),
        employment: await sqliteDataManager.getEmploymentData(),
        users: await sqliteDataManager.getUserData(),
        surveys: await sqliteDataManager.getSurveyData(),
        settings: await sqliteDataManager.getSettingsData(),
        survey_responses: await sqliteDataManager.getSurveyResponses()
      };

      // Compare counts
      const localCounts = {
        alumni: localData.alumni.length,
        employment: localData.employment.length,
        users: localData.users.length,
        surveys: localData.surveys.length,
        settings: localData.settings.length,
        survey_responses: localData.survey_responses.length
      };

      const sqliteCounts = {
        alumni: sqliteData.alumni.length,
        employment: sqliteData.employment.length,
        users: sqliteData.users.length,
        surveys: sqliteData.surveys.length,
        settings: sqliteData.settings.length,
        survey_responses: sqliteData.survey_responses.length
      };

      const matches = JSON.stringify(localCounts) === JSON.stringify(sqliteCounts);

      return {
        success: true,
        comparison: {
          localStorage: localCounts,
          sqlite: sqliteCounts,
          matches
        }
      };
    } catch (error) {
      return {
        success: false,
        comparison: {
          localStorage: {},
          sqlite: {},
          matches: false
        }
      };
    }
  }

  async getMigrationStatus(): Promise<{
    serverAvailable: boolean;
    hasLocalData: boolean;
    localDataCounts: any;
    sqliteDataCounts?: any;
  }> {
    const serverAvailable = await this.checkServerStatus();
    const localData = await this.getLocalStorageData();
    
    const localDataCounts = {
      alumni: localData.alumni.length,
      employment: localData.employment.length,
      users: localData.users.length,
      surveys: localData.surveys.length,
      settings: localData.settings.length,
      survey_responses: localData.survey_responses.length
    };

    const hasLocalData = Object.values(localDataCounts).some(count => count > 0);

    let sqliteDataCounts;
    if (serverAvailable) {
      try {
        const response = await apiClient.getMigrationStatus();
        if (response.success) {
          sqliteDataCounts = response.data.tables;
        }
      } catch (error) {
        console.error('Failed to get SQLite status:', error);
      }
    }

    return {
      serverAvailable,
      hasLocalData,
      localDataCounts,
      sqliteDataCounts
    };
  }
}

export const migrationHelper = new MigrationHelper();
export default migrationHelper;
