import DatabaseConnection from '../database/connection';
import { EmploymentData, CreateEmploymentRequest } from './types';

class EmploymentModel {
  private db;

  constructor() {
    this.db = DatabaseConnection.getInstance().getDatabase();
  }

  private generateId(): string {
    return `EMP${Date.now()}${Math.random().toString(36).substr(2, 5)}`;
  }

  create(data: CreateEmploymentRequest): EmploymentData {
    const id = this.generateId();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO employment (
        id, alumni_id, nama_perusahaan, posisi_jabatan, jenis_usaha,
        gaji_pertama, gaji_saat_ini, tanggal_mulai_kerja, status_pekerjaan,
        relevansi_pekerjaan, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      id,
      data.alumni_id,
      data.nama_perusahaan,
      data.posisi_jabatan,
      data.jenis_usaha || null,
      data.gaji_pertama || null,
      data.gaji_saat_ini || null,
      data.tanggal_mulai_kerja || null,
      data.status_pekerjaan,
      data.relevansi_pekerjaan || null,
      now,
      now
    );

    if (result.changes === 0) {
      throw new Error('Failed to create employment record');
    }

    return this.findById(id)!;
  }

  findById(id: string): EmploymentData | null {
    const stmt = this.db.prepare('SELECT * FROM employment WHERE id = ?');
    const result = stmt.get(id) as EmploymentData | undefined;
    return result || null;
  }

  findByAlumniId(alumniId: string): EmploymentData[] {
    const stmt = this.db.prepare('SELECT * FROM employment WHERE alumni_id = ? ORDER BY created_at DESC');
    return stmt.all(alumniId) as EmploymentData[];
  }

  findAll(): EmploymentData[] {
    const stmt = this.db.prepare('SELECT * FROM employment ORDER BY created_at DESC');
    return stmt.all() as EmploymentData[];
  }

  update(id: string, data: Partial<CreateEmploymentRequest>): EmploymentData | null {
    const existing = this.findById(id);
    if (!existing) {
      return null;
    }

    const updates: string[] = [];
    const params: any[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    });

    if (updates.length === 0) {
      return existing;
    }

    updates.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(id);

    const query = `UPDATE employment SET ${updates.join(', ')} WHERE id = ?`;
    const stmt = this.db.prepare(query);
    
    const result = stmt.run(...params);
    if (result.changes === 0) {
      throw new Error('Failed to update employment record');
    }

    return this.findById(id);
  }

  delete(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM employment WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  getCount(): number {
    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM employment');
    const result = stmt.get() as { count: number };
    return result.count;
  }

  getStatistics(): any {
    return {
      total: this.getCount(),
      byStatus: this.db.prepare(`
        SELECT status_pekerjaan, COUNT(*) as count 
        FROM employment 
        GROUP BY status_pekerjaan
      `).all(),
      byRelevance: this.db.prepare(`
        SELECT relevansi_pekerjaan, COUNT(*) as count 
        FROM employment 
        WHERE relevansi_pekerjaan IS NOT NULL
        GROUP BY relevansi_pekerjaan
      `).all(),
      averageSalary: this.db.prepare(`
        SELECT AVG(gaji_saat_ini) as avg_salary 
        FROM employment 
        WHERE gaji_saat_ini IS NOT NULL
      `).get(),
      topCompanies: this.db.prepare(`
        SELECT nama_perusahaan, COUNT(*) as count 
        FROM employment 
        GROUP BY nama_perusahaan 
        ORDER BY count DESC 
        LIMIT 10
      `).all()
    };
  }

  bulkInsert(employmentList: CreateEmploymentRequest[]): number {
    const stmt = this.db.prepare(`
      INSERT INTO employment (
        id, alumni_id, nama_perusahaan, posisi_jabatan, jenis_usaha,
        gaji_pertama, gaji_saat_ini, tanggal_mulai_kerja, status_pekerjaan,
        relevansi_pekerjaan, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertMany = this.db.transaction((employment: CreateEmploymentRequest[]) => {
      let count = 0;
      for (const data of employment) {
        const id = this.generateId();
        const now = new Date().toISOString();
        
        stmt.run(
          id,
          data.alumni_id,
          data.nama_perusahaan,
          data.posisi_jabatan,
          data.jenis_usaha || null,
          data.gaji_pertama || null,
          data.gaji_saat_ini || null,
          data.tanggal_mulai_kerja || null,
          data.status_pekerjaan,
          data.relevansi_pekerjaan || null,
          now,
          now
        );
        count++;
      }
      return count;
    });

    return insertMany(employmentList);
  }
}

export default EmploymentModel;
