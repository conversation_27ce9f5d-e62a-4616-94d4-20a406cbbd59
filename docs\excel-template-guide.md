# Excel Template Guide - Import Data Alumni

## Overview

Sistem tracer alumni sekarang mendukung template Excel (.xlsx) untuk import data alumni. Template ini dirancang untuk memudahkan pengguna dalam mengimpor data alumni secara batch dengan format yang terstruktur dan mudah dipahami.

## Fitur Utama

### 1. Template Excel yang Terstruktur
- **Format**: File Excel (.xlsx) dengan format yang user-friendly
- **Instruksi**: Template berisi petunjuk pengisian yang jelas
- **Contoh Data**: Disertakan contoh data untuk referensi
- **Validasi**: Kolom wajib dan format data yang jelas

### 2. Kolom Template Alumni

Template berisi kolom-kolom berikut:

| Kolom | Wajib | Deskripsi | Format |
|-------|-------|-----------|---------|
| `namaLengkap` | ✅ | Nama lengkap alumni | Text |
| `nim` | ✅ | Nomor Induk Mahasiswa | Text/Number |
| `programStudi` | ❌ | Program studi | Text |
| `fakultas` | ❌ | Fakultas | Text |
| `tahunLulus` | ❌ | Tahun kelulusan | YYYY (contoh: 2023) |
| `email` | ❌ | Email alumni | Email format |
| `noTelepon` | ❌ | Nomor telepon | Text |
| `alamat` | ❌ | Alamat lengkap | Text |
| `statusVerifikasi` | ❌ | Status verifikasi | pending/verified/rejected |

### 3. Cara Menggunakan Template

#### Download Template
1. Buka halaman **Alumni List**
2. Klik tombol **Import Data**
3. Pilih tab **Download Template**
4. Klik **Download Template Alumni.xlsx**

#### Mengisi Template
1. Buka file template yang telah didownload
2. Baca petunjuk pengisian di bagian atas file
3. Isi data alumni sesuai dengan kolom yang tersedia
4. **Hapus baris petunjuk** sebelum melakukan import
5. Simpan file dengan nama yang mudah diingat

#### Import Data
1. Kembali ke halaman **Alumni List**
2. Klik tombol **Import Data**
3. Pilih tab **Upload File**
4. Pilih file Excel yang telah diisi
5. Klik **Import Data**
6. Tunggu proses import selesai

## Validasi Data

### Validasi Wajib
- **namaLengkap**: Tidak boleh kosong
- **nim**: Tidak boleh kosong dan harus unik

### Validasi Format
- **tahunLulus**: Harus berupa angka tahun (YYYY)
- **email**: Harus mengandung karakter '@'
- **statusVerifikasi**: Hanya menerima nilai: pending, verified, rejected

### Penanganan Error
- Sistem akan menampilkan detail error untuk setiap baris yang bermasalah
- Data yang valid akan tetap diimport meskipun ada error di baris lain
- Laporan import akan menunjukkan jumlah data berhasil dan gagal

## Struktur File Template

```
TEMPLATE IMPORT DATA ALUMNI
Downloaded on: [Tanggal Download]

PETUNJUK PENGISIAN:
1. Isi data sesuai dengan kolom yang tersedia
2. Kolom namaLengkap dan nim wajib diisi
3. Format tahunLulus: YYYY (contoh: 2023)
4. Format statusVerifikasi: pending/verified/rejected
5. Hapus baris petunjuk ini sebelum import

namaLengkap | nim | programStudi | fakultas | tahunLulus | email | noTelepon | alamat | statusVerifikasi
John Doe | 123456789 | Teknik Informatika | Teknik | 2023 | <EMAIL> | 081234567890 | Jakarta | verified
Jane Smith | 987654321 | Sistem Informasi | Teknik | 2022 | <EMAIL> | 081987654321 | Bandung | pending
[Baris kosong untuk input data baru]
```

## Tips dan Best Practices

### 1. Persiapan Data
- Pastikan data sudah lengkap sebelum import
- Gunakan format yang konsisten untuk setiap kolom
- Periksa duplikasi NIM sebelum import

### 2. Format Data
- **NIM**: Gunakan format yang konsisten (angka atau kombinasi huruf-angka)
- **Email**: Pastikan format email valid
- **Tahun**: Gunakan format 4 digit (YYYY)
- **Status**: Gunakan nilai yang valid (pending/verified/rejected)

### 3. Troubleshooting
- **File tidak terbaca**: Pastikan format file adalah .xlsx
- **Data tidak valid**: Periksa kolom wajib dan format data
- **Import gagal**: Periksa ukuran file (maksimal 10MB)

## Fitur Tambahan

### 1. Backup Otomatis
- Sistem dapat membuat backup data sebelum import
- Backup disimpan dalam format JSON
- Dapat diaktifkan di pengaturan import

### 2. Preview Data
- Lihat preview data sebelum import
- Validasi format dan struktur data
- Konfirmasi sebelum proses import

### 3. Progress Tracking
- Monitor progress import secara real-time
- Informasi detail tentang proses yang sedang berjalan
- Laporan lengkap setelah import selesai

## Dukungan Format File

| Format | Ekstensi | Status |
|--------|----------|---------|
| Excel | .xlsx | ✅ Didukung |
| Excel Legacy | .xls | ✅ Didukung |
| CSV | .csv | ✅ Didukung |
| JSON | .json | ✅ Didukung |

## Batasan Sistem

- **Ukuran File**: Maksimal 10MB per file
- **Jumlah Record**: Tidak ada batasan khusus
- **Format**: Hanya mendukung format yang tercantum di atas
- **Encoding**: UTF-8 untuk karakter khusus

## Kontak Support

Jika mengalami masalah dengan template Excel atau proses import, silakan hubungi administrator sistem atau buat issue di repository project.
