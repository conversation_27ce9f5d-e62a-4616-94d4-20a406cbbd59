
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import AlumniList from "./pages/AlumniList";
import AlumniVerification from "./pages/AlumniVerification";
import Surveys from "./pages/Surveys";
import Reports from "./pages/Reports";
import DataExport from "./pages/DataExport";
import Settings from "./pages/Settings";
import Employment from "./pages/Employment";
import UserManagement from "./pages/UserManagement";
import AlumniProfile from "./pages/AlumniProfile";
import PersonalReports from "./pages/PersonalReports";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            <Route path="/alumni/list" element={
              <ProtectedRoute>
                <AlumniList />
              </ProtectedRoute>
            } />
            <Route path="/alumni/verification" element={
              <ProtectedRoute>
                <AlumniVerification />
              </ProtectedRoute>
            } />
            <Route path="/surveys" element={
              <ProtectedRoute>
                <Surveys />
              </ProtectedRoute>
            } />
            <Route path="/reports" element={
              <ProtectedRoute>
                <Reports />
              </ProtectedRoute>
            } />
            <Route path="/export" element={
              <ProtectedRoute>
                <DataExport />
              </ProtectedRoute>
            } />
            <Route path="/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />
            <Route path="/employment" element={
              <ProtectedRoute>
                <Employment />
              </ProtectedRoute>
            } />
            <Route path="/users" element={
              <ProtectedRoute>
                <UserManagement />
              </ProtectedRoute>
            } />
            <Route path="/alumni/profile" element={
              <ProtectedRoute>
                <AlumniProfile />
              </ProtectedRoute>
            } />
            <Route path="/reports/personal" element={
              <ProtectedRoute>
                <PersonalReports />
              </ProtectedRoute>
            } />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
