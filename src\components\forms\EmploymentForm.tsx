import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { EmploymentData, AlumniData } from '@/utils/dataManager';

interface EmploymentFormProps {
  employment?: EmploymentData;
  alumni: AlumniData[];
  onSubmit: (data: Omit<EmploymentData, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const EmploymentForm: React.FC<EmploymentFormProps> = ({ 
  employment, 
  alumni, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}) => {
  const [formData, setFormData] = useState({
    alumniId: '',
    companyName: '',
    position: '',
    industry: '',
    employmentType: 'full_time' as 'full_time' | 'part_time' | 'contract' | 'freelance' | 'internship',
    startDate: '',
    endDate: '',
    isCurrentJob: false,
    salary: '',
    location: '',
    description: '',
    skills: [] as string[],
    achievements: ''
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [skillInput, setSkillInput] = useState('');

  useEffect(() => {
    if (employment) {
      setFormData({
        alumniId: employment.alumniId,
        companyName: employment.companyName,
        position: employment.position,
        industry: employment.industry,
        employmentType: employment.employmentType,
        startDate: employment.startDate,
        endDate: employment.endDate || '',
        isCurrentJob: employment.isCurrentJob,
        salary: employment.salary || '',
        location: employment.location,
        description: employment.description || '',
        skills: employment.skills || [],
        achievements: employment.achievements || ''
      });
    }
  }, [employment]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.alumniId) newErrors.alumniId = 'Alumni wajib dipilih';
    if (!formData.companyName.trim()) newErrors.companyName = 'Nama perusahaan wajib diisi';
    if (!formData.position.trim()) newErrors.position = 'Posisi/jabatan wajib diisi';
    if (!formData.industry.trim()) newErrors.industry = 'Industri wajib diisi';
    if (!formData.startDate) newErrors.startDate = 'Tanggal mulai wajib diisi';
    if (!formData.isCurrentJob && !formData.endDate) {
      newErrors.endDate = 'Tanggal selesai wajib diisi jika bukan pekerjaan saat ini';
    }
    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      newErrors.endDate = 'Tanggal selesai harus setelah tanggal mulai';
    }
    if (!formData.location.trim()) newErrors.location = 'Lokasi wajib diisi';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const submitData = {
        ...formData,
        endDate: formData.isCurrentJob ? null : formData.endDate
      };
      onSubmit(submitData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addSkill = () => {
    if (skillInput.trim() && !formData.skills.includes(skillInput.trim())) {
      setFormData(prev => ({
        ...prev,
        skills: [...prev.skills, skillInput.trim()]
      }));
      setSkillInput('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSkill();
    }
  };

  const selectedAlumni = alumni.find(a => a.id === formData.alumniId);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{employment ? 'Edit Data Pekerjaan' : 'Tambah Data Pekerjaan Baru'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Alumni Selection */}
          <div>
            <Label htmlFor="alumniId">Pilih Alumni *</Label>
            <Select value={formData.alumniId} onValueChange={(value) => handleInputChange('alumniId', value)}>
              <SelectTrigger className={errors.alumniId ? 'border-red-500' : ''}>
                <SelectValue placeholder="Pilih alumni..." />
              </SelectTrigger>
              <SelectContent>
                {alumni.map(alumnus => (
                  <SelectItem key={alumnus.id} value={alumnus.id}>
                    {alumnus.name} - {alumnus.nim}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.alumniId && <p className="text-red-500 text-sm mt-1">{errors.alumniId}</p>}
            {selectedAlumni && (
              <p className="text-sm text-gray-600 mt-1">
                {selectedAlumni.program} - Lulus {selectedAlumni.graduationYear}
              </p>
            )}
          </div>

          {/* Company Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="companyName">Nama Perusahaan *</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                className={errors.companyName ? 'border-red-500' : ''}
              />
              {errors.companyName && <p className="text-red-500 text-sm mt-1">{errors.companyName}</p>}
            </div>

            <div>
              <Label htmlFor="industry">Industri *</Label>
              <Input
                id="industry"
                value={formData.industry}
                onChange={(e) => handleInputChange('industry', e.target.value)}
                className={errors.industry ? 'border-red-500' : ''}
                placeholder="Teknologi, Keuangan, Kesehatan, dll."
              />
              {errors.industry && <p className="text-red-500 text-sm mt-1">{errors.industry}</p>}
            </div>

            <div>
              <Label htmlFor="position">Posisi/Jabatan *</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                className={errors.position ? 'border-red-500' : ''}
              />
              {errors.position && <p className="text-red-500 text-sm mt-1">{errors.position}</p>}
            </div>

            <div>
              <Label htmlFor="employmentType">Tipe Pekerjaan</Label>
              <Select value={formData.employmentType} onValueChange={(value) => handleInputChange('employmentType', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full_time">Full Time</SelectItem>
                  <SelectItem value="part_time">Part Time</SelectItem>
                  <SelectItem value="contract">Kontrak</SelectItem>
                  <SelectItem value="freelance">Freelance</SelectItem>
                  <SelectItem value="internship">Magang</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="location">Lokasi *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                className={errors.location ? 'border-red-500' : ''}
                placeholder="Jakarta, Remote, dll."
              />
              {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
            </div>

            <div>
              <Label htmlFor="salary">Gaji (Opsional)</Label>
              <Input
                id="salary"
                value={formData.salary}
                onChange={(e) => handleInputChange('salary', e.target.value)}
                placeholder="Contoh: 5000000-8000000"
              />
            </div>
          </div>

          {/* Employment Period */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate">Tanggal Mulai *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                className={errors.startDate ? 'border-red-500' : ''}
              />
              {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <Label htmlFor="endDate">Tanggal Selesai</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                className={errors.endDate ? 'border-red-500' : ''}
                disabled={formData.isCurrentJob}
              />
              {errors.endDate && <p className="text-red-500 text-sm mt-1">{errors.endDate}</p>}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={formData.isCurrentJob}
              onCheckedChange={(checked) => {
                handleInputChange('isCurrentJob', checked);
                if (checked) {
                  handleInputChange('endDate', '');
                }
              }}
            />
            <Label>Masih bekerja di sini</Label>
          </div>

          {/* Skills */}
          <div>
            <Label>Keahlian/Skills</Label>
            <div className="flex space-x-2 mt-2">
              <Input
                value={skillInput}
                onChange={(e) => setSkillInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Tambahkan keahlian..."
              />
              <Button type="button" onClick={addSkill} variant="outline">
                Tambah
              </Button>
            </div>
            {formData.skills.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center"
                  >
                    {skill}
                    <button
                      type="button"
                      onClick={() => removeSkill(skill)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Deskripsi Pekerjaan</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              placeholder="Jelaskan tanggung jawab dan aktivitas pekerjaan..."
            />
          </div>

          {/* Achievements */}
          <div>
            <Label htmlFor="achievements">Pencapaian</Label>
            <Textarea
              id="achievements"
              value={formData.achievements}
              onChange={(e) => handleInputChange('achievements', e.target.value)}
              rows={3}
              placeholder="Pencapaian atau prestasi selama bekerja..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Menyimpan...' : employment ? 'Update Data' : 'Tambah Data'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default EmploymentForm;
