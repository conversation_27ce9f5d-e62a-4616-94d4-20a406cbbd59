// Test file to verify Excel functionality
// This file can be used to test the Excel template generation and parsing

import { generateExcelTemplate, parseExcelFile, getAlumniTemplateConfig } from '../utils/excelUtils';

// Test function to generate alumni template
export const testAlumniTemplate = () => {
  console.log('Testing Alumni Excel Template Generation...');
  
  try {
    const config = getAlumniTemplateConfig();
    console.log('Template config:', config);
    
    // This would normally trigger a download
    // generateExcelTemplate(config);
    
    console.log('✅ Alumni template configuration is valid');
    return true;
  } catch (error) {
    console.error('❌ Error generating alumni template:', error);
    return false;
  }
};

// Test function to verify Excel parsing (would need an actual file)
export const testExcelParsing = async (file: File) => {
  console.log('Testing Excel File Parsing...');
  
  try {
    const data = await parseExcelFile(file);
    console.log('Parsed data:', data);
    
    // Validate that we have the expected structure
    if (Array.isArray(data) && data.length > 0) {
      const firstRow = data[0];
      const expectedFields = ['namaLengkap', 'nim', 'programStudi', 'fakultas'];
      
      const hasRequiredFields = expectedFields.some(field => 
        Object.keys(firstRow).includes(field)
      );
      
      if (hasRequiredFields) {
        console.log('✅ Excel parsing successful');
        return { success: true, data };
      } else {
        console.log('⚠️ Excel file missing expected fields');
        return { success: false, error: 'Missing required fields' };
      }
    } else {
      console.log('⚠️ No data found in Excel file');
      return { success: false, error: 'No data found' };
    }
  } catch (error) {
    console.error('❌ Error parsing Excel file:', error);
    return { success: false, error };
  }
};

// Sample data for testing
export const sampleAlumniData = [
  {
    namaLengkap: 'John Doe',
    nim: '123456789',
    programStudi: 'Teknik Informatika',
    fakultas: 'Teknik',
    tahunLulus: 2023,
    email: '<EMAIL>',
    noTelepon: '081234567890',
    alamat: 'Jakarta',
    statusVerifikasi: 'verified'
  },
  {
    namaLengkap: 'Jane Smith',
    nim: '987654321',
    programStudi: 'Sistem Informasi',
    fakultas: 'Teknik',
    tahunLulus: 2022,
    email: '<EMAIL>',
    noTelepon: '081987654321',
    alamat: 'Bandung',
    statusVerifikasi: 'pending'
  }
];

// Validation function for alumni data
export const validateAlumniData = (data: any[]) => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  data.forEach((item, index) => {
    const rowNumber = index + 1;
    
    // Required field validation
    if (!item.namaLengkap || item.namaLengkap.trim() === '') {
      errors.push(`Row ${rowNumber}: namaLengkap is required`);
    }
    
    if (!item.nim || item.nim.trim() === '') {
      errors.push(`Row ${rowNumber}: nim is required`);
    }
    
    // Format validation
    if (item.tahunLulus && (isNaN(item.tahunLulus) || item.tahunLulus < 1900 || item.tahunLulus > new Date().getFullYear() + 10)) {
      warnings.push(`Row ${rowNumber}: tahunLulus should be a valid year`);
    }
    
    if (item.email && !item.email.includes('@')) {
      warnings.push(`Row ${rowNumber}: email format may be invalid`);
    }
    
    if (item.statusVerifikasi && !['pending', 'verified', 'rejected'].includes(item.statusVerifikasi)) {
      warnings.push(`Row ${rowNumber}: statusVerifikasi should be pending, verified, or rejected`);
    }
  });
  
  return { errors, warnings };
};

// Export all test functions
export default {
  testAlumniTemplate,
  testExcelParsing,
  validateAlumniData,
  sampleAlumniData
};
