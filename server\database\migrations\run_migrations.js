// Run database migrations
const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

// Get database path from environment or use default
const dbPath = process.env.DB_PATH || path.join(__dirname, '..', '..', 'data', 'tracer_alumni.db');
console.log(`Using database at: ${dbPath}`);

// Create database connection
const db = new Database(dbPath);

// Run migrations
console.log('Running migrations...');

// Add is_active column to users table
try {
  const migrationSql = fs.readFileSync(path.join(__dirname, 'add_is_active_to_users.sql'), 'utf8');
  const statements = migrationSql.split(';').filter(stmt => stmt.trim());
  
  db.exec('BEGIN TRANSACTION');
  
  for (const stmt of statements) {
    if (stmt.trim()) {
      console.log(`Executing: ${stmt.trim()}`);
      db.exec(stmt);
    }
  }
  
  db.exec('COMMIT');
  console.log('Migration completed successfully');
} catch (error) {
  db.exec('ROLLBACK');
  console.error('Migration failed:', error);
}

// Close database connection
db.close();
