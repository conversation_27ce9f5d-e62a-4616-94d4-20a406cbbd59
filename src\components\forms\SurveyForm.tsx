import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Trash2 } from 'lucide-react';
import { SurveyData, SurveyQuestion } from '@/utils/dataManager';

interface SurveyFormProps {
  survey?: SurveyData;
  onSubmit: (data: Omit<SurveyData, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const SurveyForm: React.FC<SurveyFormProps> = ({
  survey,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'draft' as 'active' | 'inactive' | 'draft',
    startDate: '',
    endDate: '',
    targetAudience: 'all' as 'all' | 'recent_graduates' | 'experienced',
    questions: [] as SurveyQuestion[],
    responses: [] as any[]
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (survey) {
      setFormData({
        title: survey.judul || '',
        description: survey.deskripsi || '',
        status: survey.status,
        startDate: survey.tanggalMulai || '',
        endDate: survey.tanggalSelesai || '',
        targetAudience: survey.targetAlumni?.length ? 'all' : 'all', // Map from targetAlumni array
        questions: survey.questions || [],
        responses: survey.responses || []
      });
    }
  }, [survey]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) newErrors.title = 'Judul survey wajib diisi';
    if (!formData.description.trim()) newErrors.description = 'Deskripsi wajib diisi';
    if (!formData.startDate) newErrors.startDate = 'Tanggal mulai wajib diisi';
    if (!formData.endDate) newErrors.endDate = 'Tanggal selesai wajib diisi';
    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      newErrors.endDate = 'Tanggal selesai harus setelah tanggal mulai';
    }
    if (formData.questions.length === 0) {
      newErrors.questions = 'Minimal harus ada 1 pertanyaan';
    }

    // Validate questions
    formData.questions.forEach((question, index) => {
      if (!question.question.trim()) {
        newErrors[`question_${index}`] = 'Pertanyaan tidak boleh kosong';
      }
      if (question.type === 'multiple_choice' && (!question.options || question.options.length < 2)) {
        newErrors[`question_${index}_options`] = 'Pilihan ganda harus memiliki minimal 2 opsi';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Map form data to SurveyData interface format
      const surveyData = {
        judul: formData.title,
        deskripsi: formData.description,
        status: formData.status,
        tanggalMulai: formData.startDate,
        tanggalSelesai: formData.endDate,
        targetAlumni: [], // This would need proper mapping based on targetAudience
        questions: formData.questions,
        responses: formData.responses
      };
      onSubmit(surveyData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addQuestion = () => {
    const newQuestion: SurveyQuestion = {
      id: Date.now().toString(),
      question: '',
      type: 'text',
      required: false,
      options: []
    };
    setFormData(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const updateQuestion = (index: number, field: string, value: any) => {
    const updatedQuestions = [...formData.questions];
    updatedQuestions[index] = { ...updatedQuestions[index], [field]: value };
    setFormData(prev => ({ ...prev, questions: updatedQuestions }));
    
    // Clear related errors
    if (errors[`question_${index}`]) {
      setErrors(prev => ({ ...prev, [`question_${index}`]: '' }));
    }
    if (errors[`question_${index}_options`]) {
      setErrors(prev => ({ ...prev, [`question_${index}_options`]: '' }));
    }
  };

  const removeQuestion = (index: number) => {
    const updatedQuestions = formData.questions.filter((_, i) => i !== index);
    setFormData(prev => ({ ...prev, questions: updatedQuestions }));
  };

  const addOption = (questionIndex: number) => {
    const updatedQuestions = [...formData.questions];
    if (!updatedQuestions[questionIndex].options) {
      updatedQuestions[questionIndex].options = [];
    }
    updatedQuestions[questionIndex].options!.push('');
    setFormData(prev => ({ ...prev, questions: updatedQuestions }));
  };

  const updateOption = (questionIndex: number, optionIndex: number, value: string) => {
    const updatedQuestions = [...formData.questions];
    updatedQuestions[questionIndex].options![optionIndex] = value;
    setFormData(prev => ({ ...prev, questions: updatedQuestions }));
  };

  const removeOption = (questionIndex: number, optionIndex: number) => {
    const updatedQuestions = [...formData.questions];
    updatedQuestions[questionIndex].options!.splice(optionIndex, 1);
    setFormData(prev => ({ ...prev, questions: updatedQuestions }));
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{survey ? 'Edit Survey' : 'Buat Survey Baru'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="title">Judul Survey *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className={errors.title ? 'border-red-500' : ''}
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div className="md:col-span-2">
              <Label htmlFor="description">Deskripsi *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                className={errors.description ? 'border-red-500' : ''}
                rows={3}
              />
              {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
            </div>

            <div>
              <Label htmlFor="startDate">Tanggal Mulai *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                className={errors.startDate ? 'border-red-500' : ''}
              />
              {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
            </div>

            <div>
              <Label htmlFor="endDate">Tanggal Selesai *</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                className={errors.endDate ? 'border-red-500' : ''}
              />
              {errors.endDate && <p className="text-red-500 text-sm mt-1">{errors.endDate}</p>}
            </div>

            <div>
              <Label htmlFor="targetAudience">Target Responden</Label>
              <Select value={formData.targetAudience} onValueChange={(value) => handleInputChange('targetAudience', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Alumni</SelectItem>
                  <SelectItem value="recent_graduates">Alumni Baru (&lt; 2 tahun)</SelectItem>
                  <SelectItem value="experienced">Alumni Berpengalaman (&gt; 2 tahun)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Aktif</SelectItem>
                  <SelectItem value="inactive">Tidak Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Questions Section */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <Label className="text-lg font-semibold">Pertanyaan Survey</Label>
              <Button type="button" onClick={addQuestion} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Pertanyaan
              </Button>
            </div>
            
            {errors.questions && <p className="text-red-500 text-sm mb-4">{errors.questions}</p>}

            <div className="space-y-4">
              {formData.questions.map((question, questionIndex) => (
                <Card key={question.id} className="p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 mr-4">
                        <Label htmlFor={`question_${questionIndex}`}>Pertanyaan {questionIndex + 1} *</Label>
                        <Input
                          id={`question_${questionIndex}`}
                          value={question.question || ''}
                          onChange={(e) => updateQuestion(questionIndex, 'question', e.target.value)}
                          className={errors[`question_${questionIndex}`] ? 'border-red-500' : ''}
                          placeholder="Masukkan pertanyaan..."
                        />
                        {errors[`question_${questionIndex}`] && (
                          <p className="text-red-500 text-sm mt-1">{errors[`question_${questionIndex}`]}</p>
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeQuestion(questionIndex)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`type_${questionIndex}`}>Tipe Pertanyaan</Label>
                        <Select 
                          value={question.type} 
                          onValueChange={(value) => updateQuestion(questionIndex, 'type', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="text">Teks</SelectItem>
                            <SelectItem value="textarea">Teks Panjang</SelectItem>
                            <SelectItem value="multiple_choice">Pilihan Ganda</SelectItem>
                            <SelectItem value="checkbox">Checkbox</SelectItem>
                            <SelectItem value="rating">Rating</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={question.required}
                          onCheckedChange={(checked) => updateQuestion(questionIndex, 'required', checked)}
                        />
                        <Label>Wajib diisi</Label>
                      </div>
                    </div>

                    {/* Options for multiple choice and checkbox */}
                    {(question.type === 'multiple_choice' || question.type === 'checkbox') && (
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <Label>Pilihan Jawaban</Label>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => addOption(questionIndex)}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Tambah Pilihan
                          </Button>
                        </div>
                        
                        {errors[`question_${questionIndex}_options`] && (
                          <p className="text-red-500 text-sm mb-2">{errors[`question_${questionIndex}_options`]}</p>
                        )}

                        <div className="space-y-2">
                          {question.options?.map((option, optionIndex) => (
                            <div key={optionIndex} className="flex items-center space-x-2">
                              <Input
                                value={option || ''}
                                onChange={(e) => updateOption(questionIndex, optionIndex, e.target.value)}
                                placeholder={`Pilihan ${optionIndex + 1}`}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeOption(questionIndex, optionIndex)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Menyimpan...' : survey ? 'Update Survey' : 'Buat Survey'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default SurveyForm;
