// Script to add real alumni data from the screenshot
const API_BASE_URL = 'http://localhost:3002/api';

const realAlumniData = [
  {
    nim: "185420110",
    nama_lengkap: "AGUSTINA MURIP",
    program_studi: "Agribisnis",
    fakultas: "Pertanian",
    tahun_masuk: 2018,
    tahun_lulus: 2025,
    ipk: 3.5,
    email: "<EMAIL>",
    no_telepon: "081234567890",
    alamat: "Manado",
    status_verifikasi: "verified"
  },
  {
    nim: "01210014",
    nama_lengkap: "AGUSTINA SALOSSA",
    program_studi: "Agribisnis",
    fakultas: "Pertanian",
    tahun_masuk: 2021,
    tahun_lulus: 2025,
    ipk: 3.6,
    email: "<EMAIL>",
    no_telepon: "081234567891",
    alamat: "Manado",
    status_verifikasi: "verified"
  },
  {
    nim: "195420110",
    nama_lengkap: "AGUSTINUS MODEONG",
    program_studi: "Agribisnis",
    fakultas: "Pertanian",
    tahun_masuk: 2019,
    tahun_lulus: 2025,
    ipk: 3.7,
    email: "<EMAIL>",
    no_telepon: "081234567892",
    alamat: "Manado",
    status_verifikasi: "verified"
  },
  {
    nim: "9210058",
    nama_lengkap: "AHMAD SHODIQUN",
    program_studi: "Agribisnis",
    fakultas: "Pertanian",
    tahun_masuk: 2021,
    tahun_lulus: 2025,
    ipk: 3.8,
    email: "<EMAIL>",
    no_telepon: "081234567893",
    alamat: "Manado",
    status_verifikasi: "verified"
  }
];

async function addRealAlumniData() {
  console.log('🎓 Adding real alumni data...\n');

  try {
    for (let i = 0; i < realAlumniData.length; i++) {
      const alumni = realAlumniData[i];
      console.log(`Adding ${i + 1}/${realAlumniData.length}: ${alumni.nama_lengkap} (${alumni.nim})`);

      const response = await fetch(`${API_BASE_URL}/alumni`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(alumni),
      });

      const result = await response.json();
      
      if (result.success) {
        console.log(`✅ Successfully added: ${alumni.nama_lengkap}`);
      } else {
        console.log(`❌ Failed to add ${alumni.nama_lengkap}: ${result.message}`);
      }
    }

    console.log('\n🎉 Real alumni data addition completed!');

    // Now check the updated alumni list
    console.log('\n📋 Checking updated alumni list...');
    const listResponse = await fetch(`${API_BASE_URL}/alumni`);
    const listResult = await listResponse.json();
    
    if (listResult.success) {
      console.log(`✅ Total alumni in database: ${listResult.data.length}`);
      
      // Show the real alumni we just added
      const realAlumni = listResult.data.filter(a => 
        a.nama_lengkap.includes('AGUSTINA') || 
        a.nama_lengkap.includes('AGUSTINUS') || 
        a.nama_lengkap.includes('AHMAD SHODIQUN')
      );
      
      console.log('\n🎓 Real alumni in database:');
      realAlumni.forEach(alumni => {
        console.log(`   - ${alumni.nim}: ${alumni.nama_lengkap} (${alumni.program_studi})`);
      });
    }

  } catch (error) {
    console.error('❌ Error adding real alumni data:', error.message);
  }
}

// Run the script
addRealAlumniData();
