<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Alumni Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .alumni-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .loading { color: blue; }
    </style>
</head>
<body>
    <h1>Test Frontend Alumni Data Loading</h1>
    <div id="status" class="loading">Loading...</div>
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:3002/api';

        async function testFrontendAlumniData() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');

            try {
                statusDiv.textContent = 'Fetching alumni data...';
                statusDiv.className = 'loading';

                const response = await fetch(`${API_BASE_URL}/alumni`);
                const result = await response.json();

                if (result.success && result.data) {
                    statusDiv.textContent = `✅ Successfully loaded ${result.data.length} alumni records`;
                    statusDiv.className = 'success';

                    // Display alumni data
                    resultsDiv.innerHTML = '<h2>Alumni Data:</h2>';
                    
                    result.data.forEach(alumni => {
                        const alumniDiv = document.createElement('div');
                        alumniDiv.className = 'alumni-item';
                        alumniDiv.innerHTML = `
                            <strong>${alumni.nim}</strong> - ${alumni.nama_lengkap}<br>
                            <small>Program: ${alumni.program_studi || 'N/A'} | Email: ${alumni.email || 'N/A'} | Status: ${alumni.status_verifikasi}</small>
                        `;
                        resultsDiv.appendChild(alumniDiv);
                    });

                    // Highlight real alumni
                    const realAlumni = result.data.filter(a => 
                        a.nama_lengkap.includes('AGUSTINA') || 
                        a.nama_lengkap.includes('AGUSTINUS') || 
                        a.nama_lengkap.includes('AHMAD SHODIQUN')
                    );

                    if (realAlumni.length > 0) {
                        const realAlumniDiv = document.createElement('div');
                        realAlumniDiv.innerHTML = `
                            <h3 style="color: green;">Real Alumni Found (${realAlumni.length}):</h3>
                            ${realAlumni.map(a => `<div style="background: #e8f5e8; padding: 5px; margin: 5px 0;">
                                ${a.nim} - ${a.nama_lengkap} (${a.program_studi})
                            </div>`).join('')}
                        `;
                        resultsDiv.appendChild(realAlumniDiv);
                    }

                } else {
                    statusDiv.textContent = '❌ Failed to load alumni data: ' + (result.message || 'Unknown error');
                    statusDiv.className = 'error';
                }

            } catch (error) {
                statusDiv.textContent = '❌ Error: ' + error.message;
                statusDiv.className = 'error';
                console.error('Error:', error);
            }
        }

        // Run the test when page loads
        window.onload = testFrontendAlumniData;
    </script>
</body>
</html>
