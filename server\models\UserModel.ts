import DatabaseConnection from '../database/connection';
import { UserData, CreateUserRequest } from './types';

class UserModel {
  private db;

  constructor() {
    this.db = DatabaseConnection.getInstance().getDatabase();
  }

  private generateId(): string {
    return `USR${Date.now()}${Math.random().toString(36).substr(2, 5)}`;
  }

  create(data: CreateUserRequest): UserData {
    const id = this.generateId();
    const now = new Date().toISOString();

    const stmt = this.db.prepare(`
      INSERT INTO users (
        id, username, email, role, nama_lengkap, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      id,
      data.username,
      data.email,
      data.role,
      data.nama_lengkap,
      data.is_active !== undefined ? data.is_active : true, // Default to active
      now,
      now
    );

    if (result.changes === 0) {
      throw new Error('Failed to create user');
    }

    return this.findById(id)!;
  }

  findById(id: string): UserData | null {
    const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
    const result = stmt.get(id) as UserData | undefined;
    return result || null;
  }

  findByEmail(email: string): UserData | null {
    const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?');
    const result = stmt.get(email) as UserData | undefined;
    return result || null;
  }

  findByUsername(username: string): UserData | null {
    const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
    const result = stmt.get(username) as UserData | undefined;
    return result || null;
  }

  findAll(): UserData[] {
    const stmt = this.db.prepare('SELECT * FROM users ORDER BY created_at DESC');
    return stmt.all() as UserData[];
  }

  update(id: string, data: Partial<CreateUserRequest>): UserData | null {
    const existing = this.findById(id);
    if (!existing) {
      return null;
    }

    const updates: string[] = [];
    const params: any[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    });

    if (updates.length === 0) {
      return existing;
    }

    updates.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(id);

    const query = `UPDATE users SET ${updates.join(', ')} WHERE id = ?`;
    const stmt = this.db.prepare(query);
    
    const result = stmt.run(...params);
    if (result.changes === 0) {
      throw new Error('Failed to update user');
    }

    return this.findById(id);
  }

  updateLastLogin(id: string): boolean {
    const stmt = this.db.prepare('UPDATE users SET last_login = ? WHERE id = ?');
    const result = stmt.run(new Date().toISOString(), id);
    return result.changes > 0;
  }

  delete(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM users WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  getCount(): number {
    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM users');
    const result = stmt.get() as { count: number };
    return result.count;
  }

  getStatistics(): any {
    return {
      total: this.getCount(),
      byRole: this.db.prepare(`
        SELECT role, COUNT(*) as count 
        FROM users 
        GROUP BY role
      `).all(),
      recentLogins: this.db.prepare(`
        SELECT nama_lengkap, last_login 
        FROM users 
        WHERE last_login IS NOT NULL 
        ORDER BY last_login DESC 
        LIMIT 10
      `).all()
    };
  }

  // Authentication helper (basic - should be enhanced with proper password hashing)
  authenticate(email: string, password: string): UserData | null {
    // Check default admin users first
    const defaultUsers = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'alumni123' }
    ];

    const defaultUser = defaultUsers.find(u => u.email === email && u.password === password);
    if (defaultUser) {
      const user = this.findByEmail(email);
      if (user) {
        this.updateLastLogin(user.id);
        return user;
      }
    }

    // Check alumni users (password = NIM)
    const user = this.findByEmail(email);
    if (user && user.role === 'alumni') {
      // Get alumni data to check NIM
      const AlumniModel = require('./AlumniModel').default;
      const alumniModel = new AlumniModel();

      // Find alumni by user_id
      const alumni = alumniModel.findByUserId(user.id);
      if (alumni && alumni.nim === password) {
        this.updateLastLogin(user.id);
        return user;
      }
    }

    return null;
  }

  bulkInsert(userList: CreateUserRequest[]): number {
    const stmt = this.db.prepare(`
      INSERT INTO users (
        id, username, email, role, nama_lengkap, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertMany = this.db.transaction((users: CreateUserRequest[]) => {
      let count = 0;
      for (const data of users) {
        const id = this.generateId();
        const now = new Date().toISOString();

        try {
          stmt.run(
            id,
            data.username,
            data.email,
            data.role,
            data.nama_lengkap,
            data.is_active !== undefined ? data.is_active : true, // Default to active
            now,
            now
          );
          count++;
        } catch (error) {
          // Skip duplicates or invalid data
          console.warn(`Skipping user ${data.email}:`, error);
        }
      }
      return count;
    });

    return insertMany(userList);
  }
}

export default UserModel;
