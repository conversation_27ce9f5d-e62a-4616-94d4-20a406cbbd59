
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserPlus, FileText, Download, Settings, Mail, BarChart3 } from 'lucide-react';

interface QuickAction {
  icon: React.ReactNode;
  title: string;
  description: string;
  action: string;
  color: string;
}

const quickActions: QuickAction[] = [
  {
    icon: <UserPlus className="h-5 w-5" />,
    title: 'Tambah Alumni Baru',
    description: 'Daftarkan alumni baru ke sistem',
    action: 'Daftar Alumni',
    color: 'bg-blue-500 hover:bg-blue-600'
  },
  {
    icon: <FileText className="h-5 w-5" />,
    title: 'Buat Survey Baru',
    description: 'Buat kuesioner tracer study',
    action: 'Buat Survey',
    color: 'bg-green-500 hover:bg-green-600'
  },
  {
    icon: <Download className="h-5 w-5" />,
    title: 'Export Data',
    description: 'Unduh data alumni dalam Excel/PDF',
    action: 'Export Data',
    color: 'bg-purple-500 hover:bg-purple-600'
  },
  {
    icon: <BarChart3 className="h-5 w-5" />,
    title: 'Generate Laporan',
    description: 'Buat laporan statistik terbaru',
    action: 'Buat Laporan',
    color: 'bg-orange-500 hover:bg-orange-600'
  },
  {
    icon: <Mail className="h-5 w-5" />,
    title: 'Kirim Reminder',
    description: 'Kirim pengingat survey ke alumni',
    action: 'Kirim Email',
    color: 'bg-red-500 hover:bg-red-600'
  },
  {
    icon: <Settings className="h-5 w-5" />,
    title: 'Pengaturan Sistem',
    description: 'Konfigurasi pengaturan aplikasi',
    action: 'Pengaturan',
    color: 'bg-gray-500 hover:bg-gray-600'
  }
];

const QuickActions = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Aksi Cepat</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg text-white ${action.color}`}>
                  {action.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">{action.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{action.description}</p>
                  <Button size="sm" variant="outline" className="text-xs">
                    {action.action}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickActions;
