import express from 'express';
import UserModel from '../models/UserModel';
import { CreateUserRequest } from '../models/types';

const router = express.Router();
const userModel = new UserModel();

// GET /api/users - Get all users
router.get('/', (req, res) => {
  try {
    const users = userModel.findAll();
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/users/statistics - Get user statistics
router.get('/statistics', (req, res) => {
  try {
    const statistics = userModel.getStatistics();
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/users/:id - Get user by ID
router.get('/:id', (req, res) => {
  try {
    const user = userModel.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/users - Create new user
router.post('/', (req, res) => {
  try {
    const data: CreateUserRequest = req.body;

    // Validate required fields
    if (!data.username || !data.email || !data.nama_lengkap) {
      return res.status(400).json({
        success: false,
        message: 'username, email, and nama_lengkap are required'
      });
    }

    // Check if email already exists
    const existingEmail = userModel.findByEmail(data.email);
    if (existingEmail) {
      return res.status(400).json({
        success: false,
        message: 'Email already exists'
      });
    }

    // Check if username already exists
    const existingUsername = userModel.findByUsername(data.username);
    if (existingUsername) {
      return res.status(400).json({
        success: false,
        message: 'Username already exists'
      });
    }

    const user = userModel.create(data);
    res.status(201).json({
      success: true,
      data: user,
      message: 'User created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/users/:id - Update user
router.put('/:id', (req, res) => {
  try {
    const data: Partial<CreateUserRequest> = req.body;
    const user = userModel.update(req.params.id, data);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user,
      message: 'User updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/users/:id - Delete user
router.delete('/:id', (req, res) => {
  try {
    const success = userModel.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/users/authenticate - Authenticate user
router.post('/authenticate', (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    const user = userModel.authenticate(email, password);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    res.json({
      success: true,
      data: user,
      message: 'Authentication successful'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Authentication failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/users/generate-from-alumni - Generate user accounts from alumni data
router.post('/generate-from-alumni', (req, res) => {
  try {
    const alumniModel = new (require('../models/AlumniModel').default)();

    // Get all alumni with email addresses
    const alumni = alumniModel.findAll({}).filter((alumni: any) => alumni.email && alumni.email.trim() !== '');

    if (alumni.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No alumni with email addresses found'
      });
    }

    const usersToCreate = [];
    const skippedAlumni = [];

    for (const alumnus of alumni) {
      // Check if user already exists
      const existingUser = userModel.findByEmail(alumnus.email);
      if (existingUser) {
        skippedAlumni.push({
          nim: alumnus.nim,
          nama_lengkap: alumnus.nama_lengkap,
          email: alumnus.email,
          reason: 'User already exists'
        });
        continue;
      }

      // Create username from NIM
      const username = alumnus.nim;

      // Check if username already exists
      const existingUsername = userModel.findByUsername(username);
      if (existingUsername) {
        skippedAlumni.push({
          nim: alumnus.nim,
          nama_lengkap: alumnus.nama_lengkap,
          email: alumnus.email,
          reason: 'Username (NIM) already exists'
        });
        continue;
      }

      usersToCreate.push({
        username: username,
        email: alumnus.email,
        role: 'alumni',
        nama_lengkap: alumnus.nama_lengkap,
        is_active: true, // Set users as active by default
        alumni_id: alumnus.id,
        nim: alumnus.nim
      });
    }

    if (usersToCreate.length === 0) {
      return res.json({
        success: true,
        data: {
          created: 0,
          skipped: skippedAlumni.length,
          skippedAlumni: skippedAlumni
        },
        message: 'No new users to create'
      });
    }

    // Create users in bulk
    const createdCount = userModel.bulkInsert(usersToCreate);

    // Update alumni records with user_id
    const db = userModel.db;
    const updateStmt = db.prepare('UPDATE alumni SET user_id = ? WHERE id = ?');
    const updateMany = db.transaction((updates: any[]) => {
      for (const update of updates) {
        const user = userModel.findByEmail(update.email);
        if (user) {
          updateStmt.run(user.id, update.alumni_id);
        }
      }
    });

    updateMany(usersToCreate);

    res.json({
      success: true,
      data: {
        created: createdCount,
        skipped: skippedAlumni.length,
        skippedAlumni: skippedAlumni
      },
      message: `Successfully created ${createdCount} user accounts from alumni data`
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to generate users from alumni',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/users/bulk - Bulk insert users
router.post('/bulk', (req, res) => {
  try {
    const userList: CreateUserRequest[] = req.body;
    
    if (!Array.isArray(userList) || userList.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid data format. Expected array of user objects.'
      });
    }

    const count = userModel.bulkInsert(userList);
    res.json({
      success: true,
      message: `Successfully inserted ${count} user records`,
      data: { count }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to bulk insert users',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
