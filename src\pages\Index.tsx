
import React from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import DashboardStats from '@/components/DashboardStats';
import EmploymentChart from '@/components/EmploymentChart';
import RecentActivity from '@/components/RecentActivity';
import QuickActions from '@/components/QuickActions';

const Index = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        <Sidebar />
        
        {/* Main Content */}
        <main className="flex-1 ml-64 p-6">
          <div className="max-w-7xl mx-auto">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard Tracer Alumni</h1>
              <p className="text-gray-600">
                Selamat datang di sistem tracer study alumni. Pantau statistik dan kelola data alumni dengan mudah.
              </p>
            </div>

            {/* Stats Cards */}
            <DashboardStats />

            {/* Charts Section */}
            <div className="mb-8">
              <EmploymentChart />
            </div>

            {/* Bottom Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <RecentActivity />
              <QuickActions />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
