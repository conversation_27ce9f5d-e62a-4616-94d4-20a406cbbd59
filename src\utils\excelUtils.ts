import * as XLSX from 'xlsx';

export interface ExcelTemplateConfig {
  title: string;
  instructions: string[];
  headers: string[];
  sampleData: any[];
  filename: string;
  columnWidths?: number[];
}

/**
 * Generate and download an Excel template file
 */
export const generateExcelTemplate = (config: ExcelTemplateConfig) => {
  // Create a new workbook
  const workbook = XLSX.utils.book_new();
  
  // Build template data array
  const templateData: any[][] = [];
  
  // Add title
  templateData.push([config.title]);
  templateData.push([`Downloaded on: ${new Date().toLocaleDateString('id-ID')}`]);
  templateData.push(['']);
  
  // Add instructions
  if (config.instructions.length > 0) {
    templateData.push(['PETUNJUK PENGISIAN:']);
    config.instructions.forEach((instruction, index) => {
      templateData.push([`${index + 1}. ${instruction}`]);
    });
    templateData.push(['']);
  }
  
  // Add headers
  templateData.push(config.headers);
  
  // Add sample data
  config.sampleData.forEach(row => {
    const rowData = config.headers.map(header => row[header] || '');
    templateData.push(rowData);
  });
  
  // Add empty row for user input
  templateData.push(new Array(config.headers.length).fill(''));
  
  // Create worksheet from the template data
  const worksheet = XLSX.utils.aoa_to_sheet(templateData);
  
  // Set column widths if provided
  if (config.columnWidths) {
    worksheet['!cols'] = config.columnWidths.map(width => ({ wch: width }));
  } else {
    // Default column widths based on header length
    worksheet['!cols'] = config.headers.map(header => ({ 
      wch: Math.max(header.length + 5, 15) 
    }));
  }
  
  // Add the worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Template');
  
  // Generate Excel file and download
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = config.filename;
  link.click();
  
  // Clean up
  URL.revokeObjectURL(link.href);
};

/**
 * Parse Excel file and return data as array of objects
 */
export const parseExcelFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get the first worksheet
        const worksheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[worksheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // Find the header row (contains column names)
        let headerRowIndex = -1;
        for (let i = 0; i < jsonData.length; i++) {
          const row = jsonData[i] as any[];
          if (row && row.some(cell => 
            typeof cell === 'string' && 
            (cell.includes('namaLengkap') || cell.includes('nim') || 
             cell.includes('name') || cell.includes('id'))
          )) {
            headerRowIndex = i;
            break;
          }
        }
        
        if (headerRowIndex === -1) {
          throw new Error('Header row not found in Excel file');
        }
        
        const headers = jsonData[headerRowIndex] as string[];
        const result: any[] = [];
        
        // Process data rows
        for (let i = headerRowIndex + 1; i < jsonData.length; i++) {
          const row = jsonData[i] as any[];
          if (row && row.some(cell => cell !== undefined && cell !== '')) {
            const rowData: any = {};
            headers.forEach((header, index) => {
              if (header && row[index] !== undefined) {
                rowData[header] = row[index];
              }
            });
            
            // Only add rows that have some data
            if (Object.values(rowData).some(value => value !== undefined && value !== '')) {
              result.push(rowData);
            }
          }
        }
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = (e) => reject(e);
    reader.readAsArrayBuffer(file);
  });
};

/**
 * Alumni template configuration
 */
export const getAlumniTemplateConfig = (): ExcelTemplateConfig => ({
  title: 'TEMPLATE IMPORT DATA ALUMNI',
  instructions: [
    'Isi data sesuai dengan kolom yang tersedia',
    'Kolom namaLengkap dan nim wajib diisi',
    'Format tahunLulus: YYYY (contoh: 2023)',
    'Format statusVerifikasi: pending/verified/rejected',
    'Hapus baris petunjuk ini sebelum import'
  ],
  headers: [
    'namaLengkap', 'nim', 'programStudi', 'fakultas', 
    'tahunLulus', 'email', 'noTelepon', 'alamat', 'statusVerifikasi'
  ],
  sampleData: [
    {
      namaLengkap: 'John Doe',
      nim: '123456789',
      programStudi: 'Teknik Informatika',
      fakultas: 'Teknik',
      tahunLulus: 2023,
      email: '<EMAIL>',
      noTelepon: '081234567890',
      alamat: 'Jakarta',
      statusVerifikasi: 'verified'
    },
    {
      namaLengkap: 'Jane Smith',
      nim: '987654321',
      programStudi: 'Sistem Informasi',
      fakultas: 'Teknik',
      tahunLulus: 2022,
      email: '<EMAIL>',
      noTelepon: '081987654321',
      alamat: 'Bandung',
      statusVerifikasi: 'pending'
    }
  ],
  filename: `template_alumni_${new Date().toISOString().split('T')[0]}.xlsx`,
  columnWidths: [20, 12, 20, 15, 12, 25, 15, 30, 15]
});
