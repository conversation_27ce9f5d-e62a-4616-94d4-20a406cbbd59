import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { AlumniData } from '@/utils/dataManager';

interface AlumniFormProps {
  alumni?: AlumniData;
  onSubmit: (data: Omit<AlumniData, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const AlumniForm: React.FC<AlumniFormProps> = ({ alumni, onSubmit, onCancel, isLoading = false }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    graduationYear: new Date().getFullYear(),
    program: '',
    faculty: '',
    nim: '',
    address: '',
    dateOfBirth: '',
    gender: 'male' as 'male' | 'female',
    status: 'pending' as 'verified' | 'pending' | 'rejected',
    profilePicture: '',
    socialMedia: {
      linkedin: '',
      instagram: '',
      facebook: '',
      twitter: ''
    },
    notes: ''
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (alumni) {
      setFormData({
        name: alumni.namaLengkap || '',
        email: alumni.email || '',
        phone: alumni.noTelepon || '',
        graduationYear: alumni.tahunLulus || new Date().getFullYear(),
        program: alumni.programStudi || '',
        faculty: alumni.fakultas || '',
        nim: alumni.nim || '',
        address: alumni.alamat || '',
        dateOfBirth: alumni.createdAt || '',
        gender: 'male' as 'male' | 'female',
        status: alumni.statusVerifikasi || 'pending',
        profilePicture: '',
        socialMedia: {
          linkedin: '',
          instagram: '',
          facebook: '',
          twitter: ''
        },
        notes: ''
      });
    }
  }, [alumni]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) newErrors.name = 'Nama wajib diisi';
    if (!formData.email.trim()) newErrors.email = 'Email wajib diisi';
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }
    if (!formData.phone.trim()) newErrors.phone = 'Nomor telepon wajib diisi';
    if (!formData.program.trim()) newErrors.program = 'Program studi wajib diisi';
    if (!formData.faculty.trim()) newErrors.faculty = 'Fakultas wajib diisi';
    if (!formData.nim.trim()) newErrors.nim = 'NIM wajib diisi';
    if (formData.graduationYear < 1900 || formData.graduationYear > new Date().getFullYear() + 10) {
      newErrors.graduationYear = 'Tahun lulus tidak valid';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSocialMediaChange = (platform: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      socialMedia: { ...prev.socialMedia, [platform]: value }
    }));
  };

  const currentYear = new Date().getFullYear();
  const graduationYears = Array.from({ length: 50 }, (_, i) => currentYear - i + 5);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{alumni ? 'Edit Alumni' : 'Tambah Alumni Baru'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Nama Lengkap *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            </div>

            <div>
              <Label htmlFor="nim">NIM *</Label>
              <Input
                id="nim"
                value={formData.nim}
                onChange={(e) => handleInputChange('nim', e.target.value)}
                className={errors.nim ? 'border-red-500' : ''}
              />
              {errors.nim && <p className="text-red-500 text-sm mt-1">{errors.nim}</p>}
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div>
              <Label htmlFor="phone">Nomor Telepon *</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
            </div>

            <div>
              <Label htmlFor="dateOfBirth">Tanggal Lahir</Label>
              <Input
                id="dateOfBirth"
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="gender">Jenis Kelamin</Label>
              <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Laki-laki</SelectItem>
                  <SelectItem value="female">Perempuan</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Academic Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="faculty">Fakultas *</Label>
              <Input
                id="faculty"
                value={formData.faculty}
                onChange={(e) => handleInputChange('faculty', e.target.value)}
                className={errors.faculty ? 'border-red-500' : ''}
              />
              {errors.faculty && <p className="text-red-500 text-sm mt-1">{errors.faculty}</p>}
            </div>

            <div>
              <Label htmlFor="program">Program Studi *</Label>
              <Input
                id="program"
                value={formData.program}
                onChange={(e) => handleInputChange('program', e.target.value)}
                className={errors.program ? 'border-red-500' : ''}
              />
              {errors.program && <p className="text-red-500 text-sm mt-1">{errors.program}</p>}
            </div>

            <div>
              <Label htmlFor="graduationYear">Tahun Lulus *</Label>
              <Select
                value={formData.graduationYear?.toString() || ''}
                onValueChange={(value) => handleInputChange('graduationYear', parseInt(value))}
              >
                <SelectTrigger className={errors.graduationYear ? 'border-red-500' : ''}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {graduationYears.map(year => (
                    <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.graduationYear && <p className="text-red-500 text-sm mt-1">{errors.graduationYear}</p>}
            </div>
          </div>

          {/* Address */}
          <div>
            <Label htmlFor="address">Alamat</Label>
            <Textarea
              id="address"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              rows={3}
            />
          </div>

          {/* Social Media */}
          <div>
            <Label>Media Sosial</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
              <div>
                <Label htmlFor="linkedin" className="text-sm">LinkedIn</Label>
                <Input
                  id="linkedin"
                  value={formData.socialMedia.linkedin}
                  onChange={(e) => handleSocialMediaChange('linkedin', e.target.value)}
                  placeholder="https://linkedin.com/in/username"
                />
              </div>
              <div>
                <Label htmlFor="instagram" className="text-sm">Instagram</Label>
                <Input
                  id="instagram"
                  value={formData.socialMedia.instagram}
                  onChange={(e) => handleSocialMediaChange('instagram', e.target.value)}
                  placeholder="@username"
                />
              </div>
              <div>
                <Label htmlFor="facebook" className="text-sm">Facebook</Label>
                <Input
                  id="facebook"
                  value={formData.socialMedia.facebook}
                  onChange={(e) => handleSocialMediaChange('facebook', e.target.value)}
                  placeholder="https://facebook.com/username"
                />
              </div>
              <div>
                <Label htmlFor="twitter" className="text-sm">Twitter</Label>
                <Input
                  id="twitter"
                  value={formData.socialMedia.twitter}
                  onChange={(e) => handleSocialMediaChange('twitter', e.target.value)}
                  placeholder="@username"
                />
              </div>
            </div>
          </div>

          {/* Status (only for edit) */}
          {alumni && (
            <div>
              <Label htmlFor="status">Status Verifikasi</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="verified">Terverifikasi</SelectItem>
                  <SelectItem value="pending">Menunggu Verifikasi</SelectItem>
                  <SelectItem value="rejected">Ditolak</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Catatan</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              rows={3}
              placeholder="Catatan tambahan..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Batal
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Menyimpan...' : alumni ? 'Update Alumni' : 'Tambah Alumni'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default AlumniForm;
