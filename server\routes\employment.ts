import express from 'express';
import EmploymentModel from '../models/EmploymentModel';
import { CreateEmploymentRequest } from '../models/types';

const router = express.Router();
const employmentModel = new EmploymentModel();

// GET /api/employment - Get all employment records
router.get('/', (req, res) => {
  try {
    const employment = employmentModel.findAll();
    res.json({
      success: true,
      data: employment
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employment records',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/employment/statistics - Get employment statistics
router.get('/statistics', (req, res) => {
  try {
    const statistics = employmentModel.getStatistics();
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch statistics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/employment/:id - Get employment by ID
router.get('/:id', (req, res) => {
  try {
    const employment = employmentModel.findById(req.params.id);
    if (!employment) {
      return res.status(404).json({
        success: false,
        message: 'Employment record not found'
      });
    }

    res.json({
      success: true,
      data: employment
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employment record',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/employment/alumni/:alumniId - Get employment records by alumni ID
router.get('/alumni/:alumniId', (req, res) => {
  try {
    const employment = employmentModel.findByAlumniId(req.params.alumniId);
    res.json({
      success: true,
      data: employment
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch employment records',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/employment - Create new employment record
router.post('/', (req, res) => {
  try {
    const data: CreateEmploymentRequest = req.body;

    // Validate required fields
    if (!data.alumni_id || !data.nama_perusahaan || !data.posisi_jabatan || !data.status_pekerjaan) {
      return res.status(400).json({
        success: false,
        message: 'alumni_id, nama_perusahaan, posisi_jabatan, and status_pekerjaan are required'
      });
    }

    const employment = employmentModel.create(data);
    res.status(201).json({
      success: true,
      data: employment,
      message: 'Employment record created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create employment record',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/employment/:id - Update employment record
router.put('/:id', (req, res) => {
  try {
    const data: Partial<CreateEmploymentRequest> = req.body;
    const employment = employmentModel.update(req.params.id, data);
    
    if (!employment) {
      return res.status(404).json({
        success: false,
        message: 'Employment record not found'
      });
    }

    res.json({
      success: true,
      data: employment,
      message: 'Employment record updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update employment record',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/employment/:id - Delete employment record
router.delete('/:id', (req, res) => {
  try {
    const success = employmentModel.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Employment record not found'
      });
    }

    res.json({
      success: true,
      message: 'Employment record deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete employment record',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/employment/bulk - Bulk insert employment records
router.post('/bulk', (req, res) => {
  try {
    const employmentList: CreateEmploymentRequest[] = req.body;
    
    if (!Array.isArray(employmentList) || employmentList.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Invalid data format. Expected array of employment objects.'
      });
    }

    const count = employmentModel.bulkInsert(employmentList);
    res.json({
      success: true,
      message: `Successfully inserted ${count} employment records`,
      data: { count }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to bulk insert employment records',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
