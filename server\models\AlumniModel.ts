import DatabaseConnection from '../database/connection';
import { AlumniData, CreateAlumniRequest, UpdateAlumniRequest, FilterParams, PaginationParams } from './types';

class AlumniModel {
  private db;

  constructor() {
    this.db = DatabaseConnection.getInstance().getDatabase();
  }

  // Generate unique ID for alumni
  private generateId(): string {
    const currentYear = new Date().getFullYear();
    const count = this.getCount() + 1;
    return `ALM${currentYear}${count.toString().padStart(4, '0')}`;
  }

  // Create new alumni
  create(data: CreateAlumniRequest): AlumniData {
    const id = this.generateId();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO alumni (
        id, user_id, nim, nama_lengkap, program_studi, fakultas,
        tahun_masuk, tahun_lulus, ipk, email, no_telepon, alamat,
        status_verifikasi, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      id,
      data.user_id || null,
      data.nim,
      data.nama_lengkap,
      data.program_studi || null,
      data.fakultas || null,
      data.tahun_masuk || null,
      data.tahun_lulus || null,
      data.ipk || null,
      data.email || null,
      data.no_telepon || null,
      data.alamat || null,
      data.status_verifikasi || 'pending',
      now,
      now
    );

    if (result.changes === 0) {
      throw new Error('Failed to create alumni');
    }

    return this.findById(id)!;
  }

  // Find alumni by ID
  findById(id: string): AlumniData | null {
    const stmt = this.db.prepare('SELECT * FROM alumni WHERE id = ?');
    const result = stmt.get(id) as AlumniData | undefined;
    return result || null;
  }

  // Find alumni by NIM
  findByNim(nim: string): AlumniData | null {
    const stmt = this.db.prepare('SELECT * FROM alumni WHERE nim = ?');
    const result = stmt.get(nim) as AlumniData | undefined;
    return result || null;
  }

  // Find alumni by user_id
  findByUserId(userId: string): AlumniData | null {
    const stmt = this.db.prepare('SELECT * FROM alumni WHERE user_id = ?');
    const result = stmt.get(userId) as AlumniData | undefined;
    return result || null;
  }

  // Find alumni by email
  findByEmail(email: string): AlumniData | null {
    const stmt = this.db.prepare('SELECT * FROM alumni WHERE email = ?');
    const result = stmt.get(email) as AlumniData | undefined;
    return result || null;
  }

  // Get all alumni with optional filtering and pagination
  findAll(filters?: FilterParams, pagination?: PaginationParams): AlumniData[] {
    let query = 'SELECT * FROM alumni WHERE 1=1';
    const params: any[] = [];

    // Apply filters
    if (filters) {
      if (filters.status) {
        query += ' AND status_verifikasi = ?';
        params.push(filters.status);
      }
      if (filters.program_studi) {
        query += ' AND program_studi = ?';
        params.push(filters.program_studi);
      }
      if (filters.fakultas) {
        query += ' AND fakultas = ?';
        params.push(filters.fakultas);
      }
      if (filters.tahun_lulus) {
        query += ' AND tahun_lulus = ?';
        params.push(filters.tahun_lulus);
      }
      if (filters.search) {
        query += ' AND (nama_lengkap LIKE ? OR nim LIKE ? OR email LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }
    }

    // Apply sorting
    if (pagination?.sortBy) {
      const sortOrder = pagination.sortOrder || 'asc';
      query += ` ORDER BY ${pagination.sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY created_at DESC';
    }

    // Apply pagination
    if (pagination?.limit) {
      const offset = ((pagination.page || 1) - 1) * pagination.limit;
      query += ' LIMIT ? OFFSET ?';
      params.push(pagination.limit, offset);
    }

    const stmt = this.db.prepare(query);
    return stmt.all(...params) as AlumniData[];
  }

  // Update alumni
  update(id: string, data: UpdateAlumniRequest): AlumniData | null {
    const existing = this.findById(id);
    if (!existing) {
      return null;
    }

    const updates: string[] = [];
    const params: any[] = [];

    // Build dynamic update query
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    });

    if (updates.length === 0) {
      return existing;
    }

    updates.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(id);

    const query = `UPDATE alumni SET ${updates.join(', ')} WHERE id = ?`;
    const stmt = this.db.prepare(query);
    
    const result = stmt.run(...params);
    if (result.changes === 0) {
      throw new Error('Failed to update alumni');
    }

    return this.findById(id);
  }

  // Delete alumni
  delete(id: string): boolean {
    const stmt = this.db.prepare('DELETE FROM alumni WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  // Get count of alumni
  getCount(filters?: FilterParams): number {
    let query = 'SELECT COUNT(*) as count FROM alumni WHERE 1=1';
    const params: any[] = [];

    // Apply same filters as findAll
    if (filters) {
      if (filters.status) {
        query += ' AND status_verifikasi = ?';
        params.push(filters.status);
      }
      if (filters.program_studi) {
        query += ' AND program_studi = ?';
        params.push(filters.program_studi);
      }
      if (filters.fakultas) {
        query += ' AND fakultas = ?';
        params.push(filters.fakultas);
      }
      if (filters.tahun_lulus) {
        query += ' AND tahun_lulus = ?';
        params.push(filters.tahun_lulus);
      }
      if (filters.search) {
        query += ' AND (nama_lengkap LIKE ? OR nim LIKE ? OR email LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }
    }

    const stmt = this.db.prepare(query);
    const result = stmt.get(...params) as { count: number };
    return result.count;
  }

  // Get alumni statistics
  getStatistics(): any {
    const stats = {
      total: this.getCount(),
      verified: this.getCount({ status: 'verified' }),
      pending: this.getCount({ status: 'pending' }),
      rejected: this.getCount({ status: 'rejected' }),
      byYear: this.db.prepare(`
        SELECT tahun_lulus, COUNT(*) as count 
        FROM alumni 
        WHERE tahun_lulus IS NOT NULL 
        GROUP BY tahun_lulus 
        ORDER BY tahun_lulus DESC
      `).all(),
      byProgram: this.db.prepare(`
        SELECT program_studi, COUNT(*) as count 
        FROM alumni 
        WHERE program_studi IS NOT NULL 
        GROUP BY program_studi 
        ORDER BY count DESC
      `).all(),
      byFaculty: this.db.prepare(`
        SELECT fakultas, COUNT(*) as count 
        FROM alumni 
        WHERE fakultas IS NOT NULL 
        GROUP BY fakultas 
        ORDER BY count DESC
      `).all()
    };

    return stats;
  }

  // Bulk insert alumni (for migration)
  bulkInsert(alumniList: CreateAlumniRequest[]): number {
    const stmt = this.db.prepare(`
      INSERT INTO alumni (
        id, user_id, nim, nama_lengkap, program_studi, fakultas,
        tahun_masuk, tahun_lulus, ipk, email, no_telepon, alamat,
        status_verifikasi, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertMany = this.db.transaction((alumni: CreateAlumniRequest[]) => {
      let count = 0;
      for (const data of alumni) {
        const id = this.generateId();
        const now = new Date().toISOString();
        
        stmt.run(
          id,
          data.user_id || null,
          data.nim,
          data.nama_lengkap,
          data.program_studi || null,
          data.fakultas || null,
          data.tahun_masuk || null,
          data.tahun_lulus || null,
          data.ipk || null,
          data.email || null,
          data.no_telepon || null,
          data.alamat || null,
          data.status_verifikasi || 'pending',
          now,
          now
        );
        count++;
      }
      return count;
    });

    return insertMany(alumniList);
  }
}

export default AlumniModel;
