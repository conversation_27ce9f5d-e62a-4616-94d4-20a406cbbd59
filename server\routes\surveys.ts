import express from 'express';
import DatabaseConnection from '../database/connection';
import { SurveyData, SurveyResponse, CreateSurveyRequest } from '../models/types';

const router = express.Router();
const db = DatabaseConnection.getInstance().getDatabase();

// Helper function to generate survey ID
const generateSurveyId = (): string => {
  const count = db.prepare('SELECT COUNT(*) as count FROM surveys').get() as { count: number };
  return `SRV${(count.count + 1).toString().padStart(4, '0')}`;
};

// Helper function to generate response ID
const generateResponseId = (): string => {
  return `RESP${Date.now()}${Math.random().toString(36).substr(2, 5)}`;
};

// GET /api/surveys - Get all surveys
router.get('/', (req, res) => {
  try {
    const stmt = db.prepare('SELECT * FROM surveys ORDER BY created_at DESC');
    const surveys = stmt.all() as SurveyData[];
    
    // Parse JSON fields
    const parsedSurveys = surveys.map(survey => ({
      ...survey,
      target_alumni: survey.target_alumni ? JSON.parse(survey.target_alumni) : [],
      questions: survey.questions ? JSON.parse(survey.questions) : []
    }));

    res.json({
      success: true,
      data: parsedSurveys
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch surveys',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/surveys/:id - Get survey by ID
router.get('/:id', (req, res) => {
  try {
    const stmt = db.prepare('SELECT * FROM surveys WHERE id = ?');
    const survey = stmt.get(req.params.id) as SurveyData | undefined;
    
    if (!survey) {
      return res.status(404).json({
        success: false,
        message: 'Survey not found'
      });
    }

    // Parse JSON fields
    const parsedSurvey = {
      ...survey,
      target_alumni: survey.target_alumni ? JSON.parse(survey.target_alumni) : [],
      questions: survey.questions ? JSON.parse(survey.questions) : []
    };

    res.json({
      success: true,
      data: parsedSurvey
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch survey',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/surveys - Create new survey
router.post('/', (req, res) => {
  try {
    const data: CreateSurveyRequest = req.body;

    if (!data.judul) {
      return res.status(400).json({
        success: false,
        message: 'judul is required'
      });
    }

    const id = generateSurveyId();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO surveys (
        id, judul, deskripsi, tanggal_mulai, tanggal_selesai,
        status, target_alumni, questions, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      id,
      data.judul,
      data.deskripsi || null,
      data.tanggal_mulai || null,
      data.tanggal_selesai || null,
      data.status || 'active',
      data.target_alumni ? JSON.stringify(data.target_alumni) : null,
      data.questions ? JSON.stringify(data.questions) : null,
      now,
      now
    );

    if (result.changes === 0) {
      throw new Error('Failed to create survey');
    }

    // Fetch and return the created survey
    const createdSurvey = db.prepare('SELECT * FROM surveys WHERE id = ?').get(id) as SurveyData;
    const parsedSurvey = {
      ...createdSurvey,
      target_alumni: createdSurvey.target_alumni ? JSON.parse(createdSurvey.target_alumni) : [],
      questions: createdSurvey.questions ? JSON.parse(createdSurvey.questions) : []
    };

    res.status(201).json({
      success: true,
      data: parsedSurvey,
      message: 'Survey created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create survey',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// PUT /api/surveys/:id - Update survey
router.put('/:id', (req, res) => {
  try {
    const data: Partial<CreateSurveyRequest> = req.body;
    
    // Check if survey exists
    const existing = db.prepare('SELECT * FROM surveys WHERE id = ?').get(req.params.id);
    if (!existing) {
      return res.status(404).json({
        success: false,
        message: 'Survey not found'
      });
    }

    const updates: string[] = [];
    const params: any[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'target_alumni' || key === 'questions') {
          updates.push(`${key} = ?`);
          params.push(JSON.stringify(value));
        } else {
          updates.push(`${key} = ?`);
          params.push(value);
        }
      }
    });

    if (updates.length === 0) {
      return res.json({
        success: true,
        data: existing,
        message: 'No changes made'
      });
    }

    updates.push('updated_at = ?');
    params.push(new Date().toISOString());
    params.push(req.params.id);

    const query = `UPDATE surveys SET ${updates.join(', ')} WHERE id = ?`;
    const stmt = db.prepare(query);
    
    const result = stmt.run(...params);
    if (result.changes === 0) {
      throw new Error('Failed to update survey');
    }

    // Fetch and return updated survey
    const updatedSurvey = db.prepare('SELECT * FROM surveys WHERE id = ?').get(req.params.id) as SurveyData;
    const parsedSurvey = {
      ...updatedSurvey,
      target_alumni: updatedSurvey.target_alumni ? JSON.parse(updatedSurvey.target_alumni) : [],
      questions: updatedSurvey.questions ? JSON.parse(updatedSurvey.questions) : []
    };

    res.json({
      success: true,
      data: parsedSurvey,
      message: 'Survey updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update survey',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// DELETE /api/surveys/:id - Delete survey
router.delete('/:id', (req, res) => {
  try {
    const stmt = db.prepare('DELETE FROM surveys WHERE id = ?');
    const result = stmt.run(req.params.id);
    
    if (result.changes === 0) {
      return res.status(404).json({
        success: false,
        message: 'Survey not found'
      });
    }

    res.json({
      success: true,
      message: 'Survey deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to delete survey',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GET /api/surveys/:id/responses - Get survey responses
router.get('/:id/responses', (req, res) => {
  try {
    const stmt = db.prepare('SELECT * FROM survey_responses WHERE survey_id = ? ORDER BY submitted_at DESC');
    const responses = stmt.all(req.params.id) as SurveyResponse[];
    
    // Parse JSON responses
    const parsedResponses = responses.map(response => ({
      ...response,
      responses: JSON.parse(response.responses)
    }));

    res.json({
      success: true,
      data: parsedResponses
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch survey responses',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// POST /api/surveys/:id/responses - Submit survey response
router.post('/:id/responses', (req, res) => {
  try {
    const { alumni_id, responses } = req.body;

    if (!alumni_id || !responses) {
      return res.status(400).json({
        success: false,
        message: 'alumni_id and responses are required'
      });
    }

    const id = generateResponseId();
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO survey_responses (id, survey_id, alumni_id, responses, submitted_at)
      VALUES (?, ?, ?, ?, ?)
    `);

    const result = stmt.run(
      id,
      req.params.id,
      alumni_id,
      JSON.stringify(responses),
      now
    );

    if (result.changes === 0) {
      throw new Error('Failed to submit survey response');
    }

    res.status(201).json({
      success: true,
      message: 'Survey response submitted successfully',
      data: { id, survey_id: req.params.id, alumni_id, responses, submitted_at: now }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to submit survey response',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
